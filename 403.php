<?php
http_response_code(403);
require_once 'config/database.php';
require_once 'includes/functions.php';

$page_title = 'ممنوع الوصول - 403';
include 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-6 text-center">
            <div class="py-5">
                <div class="mb-4">
                    <i class="fas fa-ban text-danger" style="font-size: 8rem;"></i>
                </div>
                
                <h1 class="display-4 fw-bold text-danger mb-3">403</h1>
                <h2 class="h4 mb-3">ممنوع الوصول</h2>
                <p class="text-muted mb-4">
                    عذراً، ليس لديك صلاحية للوصول إلى هذه الصفحة أو المورد المطلوب.
                </p>
                
                <div class="alert alert-warning text-start">
                    <h6><i class="fas fa-info-circle me-2"></i>أسباب محتملة:</h6>
                    <ul class="mb-0">
                        <li>لم تقم بتسجيل الدخول</li>
                        <li>نوع حسابك لا يسمح بالوصول لهذه الصفحة</li>
                        <li>تم تعليق حسابك مؤقتاً</li>
                        <li>انتهت صلاحية جلسة تسجيل الدخول</li>
                    </ul>
                </div>
                
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="index.php" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>
                        العودة للرئيسية
                    </a>
                    
                    <?php if (isLoggedIn()): ?>
                        <a href="logout.php" class="btn btn-outline-warning">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a>
                        <a href="login.php" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل دخول جديد
                        </a>
                    <?php else: ?>
                        <a href="login.php" class="btn btn-success">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </a>
                        <a href="register.php" class="btn btn-outline-success">
                            <i class="fas fa-user-plus me-2"></i>
                            إنشاء حساب
                        </a>
                    <?php endif; ?>
                </div>
                
                <div class="mt-4">
                    <p class="text-muted">
                        إذا كنت تعتقد أن هذا خطأ، يرجى 
                        <a href="contact.php" class="text-decoration-none">التواصل معنا</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
