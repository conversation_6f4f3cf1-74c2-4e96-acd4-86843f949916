# موقع صناعية - منصة قطع غيار السيارات

## 🚗 نظرة عامة

موقع **صناعية** هو منصة إلكترونية تربط بين العملاء الذين يبحثون عن قطع غيار السيارات والمحلات المتخصصة في بيعها. يوفر الموقع تجربة سهلة وسريعة للحصول على أفضل العروض والأسعار.

## ✨ الميزات الرئيسية

### للعملاء:
- 🔍 طلب قطع غيار بسهولة
- 💰 مقارنة العروض والأسعار
- ⭐ تقييم المحلات والخدمات
- 📱 واجهة سهلة الاستخدام
- 🔔 إشعارات فورية للعروض الجديدة

### للمحلات:
- 📋 استقبال طلبات العملاء
- 💼 تقديم عروض أسعار
- 📊 إدارة المخزون والطلبات
- 📈 تتبع المبيعات والأرباح
- 🏆 بناء سمعة تجارية

### للإدارة:
- 🎛️ لوحة تحكم شاملة
- 💳 إدارة المعاملات المالية
- 📊 تقارير وإحصائيات مفصلة
- ⚙️ إعدادات النظام

## 🛠️ التقنيات المستخدمة

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript
- **Framework**: Bootstrap 5 (RTL)
- **Icons**: Font Awesome 6
- **Fonts**: Google Fonts (Cairo)

## 📋 متطلبات التشغيل

- خادم ويب (Apache/Nginx)
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- امتداد PDO لـ PHP
- امتداد GD لمعالجة الصور

## 🚀 التثبيت والإعداد

### 1. تحميل الملفات
```bash
git clone [repository-url]
cd صناعية
```

### 2. إعداد قاعدة البيانات
- تأكد من تشغيل خادم MySQL
- قم بزيارة `http://localhost/صناعية/install.php`
- اتبع خطوات التثبيت

### 3. إعداد الصلاحيات
```bash
chmod 755 uploads/
chmod 755 uploads/requests/
chmod 755 uploads/offers/
chmod 755 uploads/profiles/
```

### 4. تخصيص الإعدادات
قم بتعديل ملف `config/database.php` حسب إعدادات خادمك:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'sanaeya_db');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

## 👥 حسابات التجربة

بعد التثبيت، يمكنك استخدام الحسابات التالية للتجربة:

### عميل تجريبي:
- **الإيميل**: <EMAIL>
- **كلمة المرور**: 123456

### محل تجريبي:
- **الإيميل**: <EMAIL>
- **كلمة المرور**: 123456

### مدير النظام:
- **الإيميل**: <EMAIL>
- **كلمة المرور**: admin123

## 📁 هيكل المشروع

```
صناعية/
├── config/                 # ملفات التكوين
│   └── database.php
├── includes/               # ملفات مشتركة
│   ├── header.php
│   ├── footer.php
│   └── functions.php
├── assets/                 # الموارد الثابتة
│   ├── css/
│   ├── js/
│   └── images/
├── customer/               # صفحات العملاء
│   └── dashboard.php
├── vendor/                 # صفحات المحلات
├── admin/                  # صفحات الإدارة
├── uploads/                # ملفات مرفوعة
├── database/               # ملفات قاعدة البيانات
│   └── create_database.sql
├── index.php               # الصفحة الرئيسية
├── login.php               # تسجيل الدخول
├── register.php            # التسجيل
├── request-part.php        # طلب قطعة غيار
└── install.php             # ملف التثبيت
```

## 🔧 الاستخدام

### للعملاء:
1. إنشاء حساب جديد أو تسجيل الدخول
2. الذهاب إلى "طلب قطعة غيار"
3. ملء تفاصيل السيارة والقطعة المطلوبة
4. انتظار العروض من المحلات
5. مقارنة العروض واختيار الأنسب

### للمحلات:
1. التسجيل كمحل قطع غيار
2. إكمال ملف التعريف التجاري
3. استقبال إشعارات الطلبات الجديدة
4. تقديم عروض أسعار تنافسية
5. متابعة الطلبات والمبيعات

## 💰 نماذج الربح

1. **عمولة على المعاملات**: 3-5% من قيمة كل صفقة
2. **اشتراكات المحلات**: باقات شهرية متدرجة
3. **رسوم العروض**: رسم ثابت لكل عرض
4. **الإعلانات**: إعلانات مدفوعة للمحلات
5. **خدمات إضافية**: توصيل، ضمان، تركيب

## 🔮 التطوير المستقبلي

- 📱 تطبيق Flutter للهواتف الذكية
- 🗺️ تكامل مع خرائط Google
- 💳 بوابات دفع إلكترونية
- 📧 نظام إشعارات متقدم
- 🤖 ذكاء اصطناعي لتوصيات القطع
- 📊 تحليلات متقدمة للبيانات

## 🛡️ الأمان

- تشفير كلمات المرور باستخدام bcrypt
- حماية من هجمات CSRF
- تنظيف البيانات المدخلة
- جلسات آمنة
- رفع آمن للملفات

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **الموقع**: [صناعية](http://localhost/صناعية)
- **الإيميل**: <EMAIL>
- **الهاتف**: +966 50 123 4567

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين والمطورين الذين ساعدوا في إنجاز هذا المشروع.

---

**صناعية** - أسرع طريقة للحصول على قطع غيار سيارتك 🚗✨
