# تعليمات إعداد موقع صناعية

## 🚀 خطوات التشغيل السريع

### 1. تشغيل الخادم المحلي
```bash
cd C:\xampp\htdocs\صناعية
php -S localhost:8000
```

### 2. إعد<PERSON> قاعدة البيانات
1. اذهب إلى: `http://localhost:8000/install.php`
2. اضغط "بدء التثبيت"
3. انتظر حتى اكتمال التثبيت

### 3. تحديث قاعدة البيانات (إذا لزم الأمر)
1. سجل دخول كمشرف: `<EMAIL>` / `admin123`
2. اذهب إلى: `http://localhost:8000/update-database.php`
3. اتبع التعليمات لتحديث قاعدة البيانات

## 👥 حسابات التجربة

### العملاء
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `123456`

### المحلات
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `123456`

### المشرفين
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `admin123`

## 🔧 حل المشاكل الشائعة

### مشكلة: "The requested resource was not found"
**الحل:**
1. تأكد من تشغيل الخادم على المنفذ الصحيح
2. تحقق من مسار الملفات
3. تأكد من وجود ملف `.htaccess`

### مشكلة: خطأ في قاعدة البيانات
**الحل:**
1. تأكد من تشغيل MySQL
2. تحقق من إعدادات الاتصال في `config/database.php`
3. شغل `install.php` مرة أخرى

### مشكلة: التنبيهات لا تعمل
**الحل:**
1. شغل `update-database.php`
2. تأكد من وجود جداول التنبيهات
3. تحقق من دوال التنبيهات في `includes/functions.php`

## 📱 الميزات المتاحة

### ✅ نظام المستخدمين
- تسجيل دخول وخروج
- أنواع مستخدمين مختلفة (عميل، محل، مشرف)
- ملفات شخصية

### ✅ نظام الطلبات والعروض
- طلب قطع الغيار
- تقديم العروض
- قبول/رفض العروض

### ✅ نظام التواصل
- محادثات مباشرة بين العملاء والمحلات
- رسائل فورية
- تحديث تلقائي

### ✅ نظام التنبيهات
- تنبيهات العروض الجديدة
- تنبيهات الرسائل
- تنبيهات العمولات
- عدادات في شريط التنقل

### ✅ نظام العمولات
- عمولة 3% من كل طرف (6% إجمالي)
- تتبع المدفوعات
- تذكيرات العمولات المتأخرة

## 🔗 الروابط المهمة

### الصفحات الرئيسية
- **الرئيسية:** `http://localhost:8000/`
- **تسجيل الدخول:** `http://localhost:8000/login.php`
- **التسجيل:** `http://localhost:8000/register.php`

### لوحات التحكم
- **العميل:** `http://localhost:8000/customer/dashboard.php`
- **المحل:** `http://localhost:8000/vendor/dashboard.php`
- **المشرف:** `http://localhost:8000/admin/dashboard.php`

### الميزات الجديدة
- **الرسائل (عميل):** `http://localhost:8000/customer/messages.php`
- **الرسائل (محل):** `http://localhost:8000/vendor/messages.php`
- **التنبيهات:** `http://localhost:8000/notifications.php`
- **العمولات (عميل):** `http://localhost:8000/customer/commissions.php`
- **العمولات (محل):** `http://localhost:8000/vendor/commissions.php`

## 📊 اختبار النظام

### 1. اختبار طلب قطعة غيار
1. سجل دخول كعميل
2. اذهب إلى "طلب قطعة غيار"
3. املأ البيانات واضغط إرسال

### 2. اختبار تقديم عرض
1. سجل دخول كمحل
2. اذهب إلى "الطلبات الجديدة"
3. اختر طلب وقدم عرض

### 3. اختبار المحادثة
1. بعد تقديم العرض، ستنشأ محادثة تلقائياً
2. اذهب إلى "الرسائل" لرؤية المحادثة
3. أرسل رسائل واختبر التحديث التلقائي

### 4. اختبار التنبيهات
1. لاحظ عدادات التنبيهات في شريط التنقل
2. اذهب إلى صفحة التنبيهات
3. اختبر تحديد التنبيهات كمقروءة

### 5. اختبار العمولات
1. اقبل عرض كعميل
2. تحقق من إنشاء العمولات تلقائياً
3. اذهب إلى صفحة العمولات لكلا الطرفين

## 🛠️ التطوير المستقبلي

### ميزات مقترحة
- [ ] نظام دفع إلكتروني
- [ ] تطبيق Flutter للهواتف
- [ ] نظام تقييم المحلات
- [ ] خرائط لمواقع المحلات
- [ ] نظام كوبونات وخصومات
- [ ] تقارير مالية متقدمة

### تحسينات تقنية
- [ ] تحسين الأداء
- [ ] إضافة Redis للتخزين المؤقت
- [ ] نظام طوابير للمهام الثقيلة
- [ ] API للتطبيقات الخارجية
- [ ] نظام نسخ احتياطي تلقائي

## 📞 الدعم الفني

إذا واجهت أي مشاكل:
1. تحقق من هذا الملف أولاً
2. راجع ملفات السجل في مجلد `logs/`
3. تأكد من تحديث قاعدة البيانات
4. تحقق من إعدادات PHP و MySQL

---

**تم إنشاء هذا النظام بواسطة Augment Agent**
**تاريخ آخر تحديث: 2025-01-13**
