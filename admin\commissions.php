<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'admin') {
    showAlert('يجب تسجيل الدخول كمشرف للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();

// فلترة العمولات
$status_filter = $_GET['status'] ?? '';
$user_type_filter = $_GET['user_type'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// بناء الاستعلام
$where_conditions = ["1=1"];
$params = [];

if (!empty($status_filter)) {
    $where_conditions[] = "c.overall_status = ?";
    $params[] = $status_filter;
}

if (!empty($date_from)) {
    $where_conditions[] = "DATE(c.created_at) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(c.created_at) <= ?";
    $params[] = $date_to;
}

$where_clause = implode(' AND ', $where_conditions);

// عدد العمولات الإجمالي
$count_query = "SELECT COUNT(*) as total FROM commissions c WHERE $where_clause";
$total_commissions = $db->query($count_query, $params)->fetch()['total'];
$total_pages = ceil($total_commissions / $per_page);

// جلب العمولات
$commissions_query = "SELECT c.*, 
                      pr.part_name, pr.car_make, pr.car_model, pr.car_year,
                      v.name as vendor_name, vp.business_name,
                      cu.name as customer_name, cu.phone as customer_phone,
                      o.price as offer_price
                      FROM commissions c
                      JOIN offers o ON c.offer_id = o.id
                      JOIN part_requests pr ON o.request_id = pr.id
                      JOIN users v ON c.vendor_id = v.id
                      JOIN users cu ON c.customer_id = cu.id
                      LEFT JOIN vendor_profiles vp ON v.id = vp.user_id
                      WHERE $where_clause 
                      ORDER BY c.created_at DESC 
                      LIMIT $per_page OFFSET $offset";

$commissions = $db->query($commissions_query, $params)->fetchAll();

// إحصائيات شاملة
$stats = $db->query("SELECT 
    COUNT(*) as total_commissions,
    COUNT(CASE WHEN overall_status = 'pending' THEN 1 END) as pending_commissions,
    COUNT(CASE WHEN overall_status = 'partial' THEN 1 END) as partial_commissions,
    COUNT(CASE WHEN overall_status = 'completed' THEN 1 END) as completed_commissions,
    COUNT(CASE WHEN vendor_payment_status = 'overdue' THEN 1 END) as vendor_overdue,
    COUNT(CASE WHEN customer_payment_status = 'overdue' THEN 1 END) as customer_overdue,
    COALESCE(SUM(total_commission), 0) as total_amount,
    COALESCE(SUM(CASE WHEN overall_status = 'completed' THEN total_commission ELSE 0 END), 0) as collected_amount,
    COALESCE(SUM(CASE WHEN overall_status != 'completed' THEN total_commission ELSE 0 END), 0) as pending_amount
    FROM commissions")->fetch();

$page_title = 'إدارة العمولات';
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold text-primary">
        <i class="fas fa-chart-line me-2"></i>
        إدارة العمولات
    </h2>
    <div class="d-flex gap-2">
        <a href="commission-settings.php" class="btn btn-warning">
            <i class="fas fa-cog me-1"></i>
            إعدادات العمولات
        </a>
        <a href="commission-reports.php" class="btn btn-info">
            <i class="fas fa-chart-bar me-1"></i>
            التقارير
        </a>
        <a href="dashboard.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            لوحة التحكم
        </a>
    </div>
</div>

<!-- إحصائيات العمولات -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['total_commissions']; ?></h4>
                <p class="mb-1">إجمالي العمولات</p>
                <small><?php echo formatPrice($stats['total_amount']); ?></small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['completed_commissions']; ?></h4>
                <p class="mb-1">مكتملة</p>
                <small><?php echo formatPrice($stats['collected_amount']); ?></small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['pending_commissions'] + $stats['partial_commissions']; ?></h4>
                <p class="mb-1">في الانتظار</p>
                <small><?php echo formatPrice($stats['pending_amount']); ?></small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['vendor_overdue'] + $stats['customer_overdue']; ?></h4>
                <p class="mb-1">متأخرة</p>
                <small>تحتاج متابعة</small>
            </div>
        </div>
    </div>
</div>

<!-- تنبيهات مهمة -->
<?php if ($stats['vendor_overdue'] > 0 || $stats['customer_overdue'] > 0): ?>
<div class="alert alert-warning">
    <div class="d-flex align-items-center">
        <i class="fas fa-exclamation-triangle fs-4 me-3"></i>
        <div class="flex-grow-1">
            <h6 class="mb-1">تنبيه: عمولات متأخرة!</h6>
            <p class="mb-0">
                هناك <?php echo $stats['vendor_overdue']; ?> عمولة متأخرة من المحلات و 
                <?php echo $stats['customer_overdue']; ?> عمولة متأخرة من العملاء تحتاج للمتابعة.
            </p>
        </div>
        <a href="?status=overdue" class="btn btn-light">
            <i class="fas fa-eye me-1"></i>
            عرض المتأخرة
        </a>
    </div>
</div>
<?php endif; ?>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">حالة العمولة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                    <option value="partial" <?php echo $status_filter === 'partial' ? 'selected' : ''; ?>>مدفوعة جزئياً</option>
                    <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>مكتملة</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="<?php echo $date_from; ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="<?php echo $date_to; ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>
                        فلترة
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة العمولات -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">العمولات (<?php echo $total_commissions; ?>)</h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($commissions)): ?>
            <div class="text-center py-5">
                <i class="fas fa-chart-line fs-1 text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد عمولات</h5>
                <p class="text-muted">لا توجد عمولات تطابق المعايير المحددة</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الصفقة</th>
                            <th>المحل</th>
                            <th>العميل</th>
                            <th>قيمة الصفقة</th>
                            <th>إجمالي العمولة</th>
                            <th>حالة المحل</th>
                            <th>حالة العميل</th>
                            <th>الحالة العامة</th>
                            <th>التاريخ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($commissions as $commission): ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($commission['part_name']); ?></strong>
                                        <br><small class="text-muted">
                                            <?php echo htmlspecialchars($commission['car_make'] . ' ' . $commission['car_model']); ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <?php echo htmlspecialchars($commission['business_name'] ?: $commission['vendor_name']); ?>
                                        <br><small class="text-muted">
                                            عمولة: <?php echo formatPrice($commission['vendor_commission_amount']); ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <?php echo htmlspecialchars($commission['customer_name']); ?>
                                        <br><small class="text-muted">
                                            عمولة: <?php echo formatPrice($commission['customer_commission_amount']); ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div class="fw-bold text-success">
                                        <?php echo formatPrice($commission['transaction_amount']); ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="fw-bold text-primary">
                                        <?php echo formatPrice($commission['total_commission']); ?>
                                    </div>
                                    <small class="text-muted">6% إجمالي</small>
                                </td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo $commission['vendor_payment_status'] === 'pending' ? 'warning' : 
                                            ($commission['vendor_payment_status'] === 'paid' ? 'success' : 
                                            ($commission['vendor_payment_status'] === 'overdue' ? 'danger' : 'secondary')); 
                                    ?> fs-6">
                                        <?php 
                                        $status_text = [
                                            'pending' => 'انتظار',
                                            'paid' => 'مدفوع',
                                            'overdue' => 'متأخر',
                                            'waived' => 'معفى'
                                        ];
                                        echo $status_text[$commission['vendor_payment_status']] ?? $commission['vendor_payment_status'];
                                        ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo $commission['customer_payment_status'] === 'pending' ? 'warning' : 
                                            ($commission['customer_payment_status'] === 'paid' ? 'success' : 
                                            ($commission['customer_payment_status'] === 'overdue' ? 'danger' : 'secondary')); 
                                    ?> fs-6">
                                        <?php 
                                        echo $status_text[$commission['customer_payment_status']] ?? $commission['customer_payment_status'];
                                        ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo $commission['overall_status'] === 'pending' ? 'warning' : 
                                            ($commission['overall_status'] === 'completed' ? 'success' : 'info'); 
                                    ?> fs-6">
                                        <?php 
                                        $overall_status_text = [
                                            'pending' => 'في الانتظار',
                                            'partial' => 'جزئي',
                                            'completed' => 'مكتمل'
                                        ];
                                        echo $overall_status_text[$commission['overall_status']] ?? $commission['overall_status'];
                                        ?>
                                    </span>
                                </td>
                                <td>
                                    <div><?php echo formatArabicDate($commission['created_at']); ?></div>
                                    <small class="text-muted">
                                        استحقاق: <?php echo formatArabicDate($commission['due_date']); ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="commission-details.php?id=<?php echo $commission['id']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="commission-edit.php?id=<?php echo $commission['id']; ?>">
                                                    <i class="fas fa-edit me-2"></i>تعديل
                                                </a></li>
                                                <li><a class="dropdown-item" href="commission-waive.php?id=<?php echo $commission['id']; ?>">
                                                    <i class="fas fa-gift me-2"></i>إعفاء
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="commission-invoice.php?id=<?php echo $commission['id']; ?>" target="_blank">
                                                    <i class="fas fa-file-pdf me-2"></i>فاتورة
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- ترقيم الصفحات -->
    <?php if ($total_pages > 1): ?>
        <div class="card-footer">
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>">السابق</a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>"><?php echo $i; ?></a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>">التالي</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>
