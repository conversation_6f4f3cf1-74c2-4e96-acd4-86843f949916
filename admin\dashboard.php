<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'admin') {
    showAlert('يجب تسجيل الدخول كمشرف للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();

// إحصائيات عامة
$stats = [];

// إجمالي المستخدمين
$stmt = $db->query("SELECT COUNT(*) as total FROM users");
$stats['total_users'] = $stmt->fetch()['total'];

// العملاء
$stmt = $db->query("SELECT COUNT(*) as customers FROM users WHERE user_type = 'customer'");
$stats['customers'] = $stmt->fetch()['customers'];

// المحلات
$stmt = $db->query("SELECT COUNT(*) as vendors FROM users WHERE user_type = 'vendor'");
$stats['vendors'] = $stmt->fetch()['vendors'];

// إجمالي الطلبات
$stmt = $db->query("SELECT COUNT(*) as total FROM part_requests");
$stats['total_requests'] = $stmt->fetch()['total'];

// الطلبات النشطة
$stmt = $db->query("SELECT COUNT(*) as active FROM part_requests WHERE status IN ('pending', 'active')");
$stats['active_requests'] = $stmt->fetch()['active'];

// إجمالي العروض
$stmt = $db->query("SELECT COUNT(*) as total FROM offers");
$stats['total_offers'] = $stmt->fetch()['total'];

// العروض المقبولة
$stmt = $db->query("SELECT COUNT(*) as accepted FROM offers WHERE status = 'accepted'");
$stats['accepted_offers'] = $stmt->fetch()['accepted'];

// إجمالي المعاملات
$stmt = $db->query("SELECT COUNT(*) as total, COALESCE(SUM(amount), 0) as total_amount, COALESCE(SUM(commission), 0) as total_commission FROM transactions WHERE payment_status = 'completed'");
$transaction_stats = $stmt->fetch();
$stats['total_transactions'] = $transaction_stats['total'];
$stats['total_revenue'] = $transaction_stats['total_amount'];
$stats['total_commission'] = $transaction_stats['total_commission'];

// أحدث المستخدمين
$recent_users = $db->query(
    "SELECT u.*, vp.business_name FROM users u 
     LEFT JOIN vendor_profiles vp ON u.id = vp.user_id 
     WHERE u.user_type != 'admin' 
     ORDER BY u.created_at DESC LIMIT 10"
)->fetchAll();

// أحدث الطلبات
$recent_requests = $db->query(
    "SELECT pr.*, u.name as customer_name FROM part_requests pr 
     JOIN users u ON pr.customer_id = u.id 
     ORDER BY pr.created_at DESC LIMIT 10"
)->fetchAll();

$page_title = 'لوحة تحكم المشرف';
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold text-primary">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة تحكم المشرف
    </h2>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-primary btn-sm" onclick="location.reload()">
            <i class="fas fa-sync-alt me-1"></i>
            تحديث
        </button>
        <div class="dropdown">
            <button class="btn btn-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-cog me-1"></i>
                إعدادات
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="settings.php">إعدادات النظام</a></li>
                <li><a class="dropdown-item" href="users.php">إدارة المستخدمين</a></li>
                <li><a class="dropdown-item" href="reports.php">التقارير</a></li>
            </ul>
        </div>
    </div>
</div>

<!-- الإحصائيات الرئيسية -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-white-50 mb-1">إجمالي المستخدمين</h6>
                        <h3 class="mb-0 counter" data-target="<?php echo $stats['total_users']; ?>">0</h3>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-white-50 mb-1">العملاء</h6>
                        <h3 class="mb-0 counter" data-target="<?php echo $stats['customers']; ?>">0</h3>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-user fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card bg-info text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-white-50 mb-1">المحلات</h6>
                        <h3 class="mb-0 counter" data-target="<?php echo $stats['vendors']; ?>">0</h3>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-store fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card bg-warning text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-white-50 mb-1">إجمالي الطلبات</h6>
                        <h3 class="mb-0 counter" data-target="<?php echo $stats['total_requests']; ?>">0</h3>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-list-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات إضافية -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-primary border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-clock text-primary fa-2x"></i>
                    </div>
                    <div>
                        <div class="text-muted small">الطلبات النشطة</div>
                        <div class="h5 mb-0 counter" data-target="<?php echo $stats['active_requests']; ?>">0</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-success border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-handshake text-success fa-2x"></i>
                    </div>
                    <div>
                        <div class="text-muted small">إجمالي العروض</div>
                        <div class="h5 mb-0 counter" data-target="<?php echo $stats['total_offers']; ?>">0</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-info border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-check-circle text-info fa-2x"></i>
                    </div>
                    <div>
                        <div class="text-muted small">عروض مقبولة</div>
                        <div class="h5 mb-0 counter" data-target="<?php echo $stats['accepted_offers']; ?>">0</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-warning border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-dollar-sign text-warning fa-2x"></i>
                    </div>
                    <div>
                        <div class="text-muted small">إجمالي العمولات</div>
                        <div class="h5 mb-0"><?php echo formatPrice($stats['total_commission']); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أزرار سريعة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title mb-3">إجراءات سريعة</h5>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="users.php" class="btn btn-primary">
                        <i class="fas fa-users me-2"></i>
                        إدارة المستخدمين
                    </a>
                    <a href="requests.php" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>
                        إدارة الطلبات
                    </a>
                    <a href="offers.php" class="btn btn-outline-success">
                        <i class="fas fa-handshake me-2"></i>
                        إدارة العروض
                    </a>
                    <a href="commissions.php" class="btn btn-outline-warning">
                        <i class="fas fa-percentage me-2"></i>
                        إدارة العمولات
                    </a>
                    <a href="transactions.php" class="btn btn-outline-info">
                        <i class="fas fa-credit-card me-2"></i>
                        المعاملات المالية
                    </a>
                    <a href="reports.php" class="btn btn-outline-dark">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير
                    </a>
                    <a href="settings.php" class="btn btn-outline-secondary">
                        <i class="fas fa-cog me-2"></i>
                        الإعدادات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- أحدث المستخدمين -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    أحدث المستخدمين
                </h5>
                <a href="users.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <?php if (empty($recent_users)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-users fs-1 mb-3"></i>
                        <p>لا يوجد مستخدمين بعد</p>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_users as $user): ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">
                                            <?php echo $user['name']; ?>
                                            <?php if ($user['business_name']): ?>
                                                <small class="text-muted">(<?php echo $user['business_name']; ?>)</small>
                                            <?php endif; ?>
                                        </h6>
                                        <p class="mb-1 text-muted small">
                                            <?php echo $user['email']; ?> | <?php echo $user['phone']; ?>
                                        </p>
                                        <small class="text-muted">
                                            <?php echo formatArabicDate($user['created_at']); ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-<?php echo $user['user_type'] === 'customer' ? 'primary' : 'success'; ?>">
                                            <?php echo $user['user_type'] === 'customer' ? 'عميل' : 'محل'; ?>
                                        </span>
                                        <br>
                                        <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'warning'; ?> mt-1">
                                            <?php echo $user['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- أحدث الطلبات -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>
                    أحدث الطلبات
                </h5>
                <a href="requests.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <?php if (empty($recent_requests)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fs-1 mb-3"></i>
                        <p>لا توجد طلبات بعد</p>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_requests as $request): ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1"><?php echo $request['part_name']; ?></h6>
                                        <p class="mb-1 text-muted small">
                                            <?php echo $request['car_make'] . ' ' . $request['car_model'] . ' ' . $request['car_year']; ?>
                                        </p>
                                        <p class="mb-1 text-muted small">
                                            العميل: <?php echo $request['customer_name']; ?>
                                        </p>
                                        <small class="text-muted">
                                            <?php echo formatArabicDate($request['created_at']); ?>
                                        </small>
                                    </div>
                                    <span class="badge bg-<?php 
                                        echo $request['status'] === 'pending' ? 'warning' : 
                                            ($request['status'] === 'active' ? 'success' : 
                                            ($request['status'] === 'closed' ? 'secondary' : 'danger')); 
                                    ?>">
                                        <?php 
                                        $status_text = [
                                            'pending' => 'في الانتظار',
                                            'active' => 'نشط',
                                            'closed' => 'مغلق',
                                            'cancelled' => 'ملغي'
                                        ];
                                        echo $status_text[$request['status']] ?? $request['status'];
                                        ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
