<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'admin') {
    showAlert('يجب تسجيل الدخول كمشرف للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();

// معالجة الإجراءات
if ($_POST) {
    if (isset($_POST['action']) && isset($_POST['user_id'])) {
        $user_id = intval($_POST['user_id']);
        $action = $_POST['action'];
        
        switch ($action) {
            case 'activate':
                $db->query("UPDATE users SET status = 'active' WHERE id = ?", [$user_id]);
                showAlert('تم تفعيل المستخدم بنجاح', 'success');
                break;
                
            case 'deactivate':
                $db->query("UPDATE users SET status = 'inactive' WHERE id = ?", [$user_id]);
                showAlert('تم إلغاء تفعيل المستخدم بنجاح', 'warning');
                break;
                
            case 'suspend':
                $db->query("UPDATE users SET status = 'suspended' WHERE id = ?", [$user_id]);
                showAlert('تم تعليق المستخدم بنجاح', 'warning');
                break;
                
            case 'delete':
                $db->query("DELETE FROM users WHERE id = ? AND user_type != 'admin'", [$user_id]);
                showAlert('تم حذف المستخدم بنجاح', 'success');
                break;
        }
        
        redirect('users.php');
    }
}

// فلترة وبحث
$search = $_GET['search'] ?? '';
$user_type_filter = $_GET['user_type'] ?? '';
$status_filter = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// بناء الاستعلام
$where_conditions = ["u.user_type != 'admin'"];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(u.name LIKE ? OR u.email LIKE ? OR u.phone LIKE ? OR vp.business_name LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

if (!empty($user_type_filter)) {
    $where_conditions[] = "u.user_type = ?";
    $params[] = $user_type_filter;
}

if (!empty($status_filter)) {
    $where_conditions[] = "u.status = ?";
    $params[] = $status_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// عدد المستخدمين الإجمالي
$count_query = "SELECT COUNT(*) as total FROM users u LEFT JOIN vendor_profiles vp ON u.id = vp.user_id WHERE $where_clause";
$total_users = $db->query($count_query, $params)->fetch()['total'];
$total_pages = ceil($total_users / $per_page);

// جلب المستخدمين
$users_query = "SELECT u.*, vp.business_name, vp.city, vp.rating 
                FROM users u 
                LEFT JOIN vendor_profiles vp ON u.id = vp.user_id 
                WHERE $where_clause 
                ORDER BY u.created_at DESC 
                LIMIT $per_page OFFSET $offset";

$users = $db->query($users_query, $params)->fetchAll();

$page_title = 'إدارة المستخدمين';
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold text-primary">
        <i class="fas fa-users me-2"></i>
        إدارة المستخدمين
    </h2>
    <div class="d-flex gap-2">
        <a href="dashboard.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4><?php echo $total_users; ?></h4>
                <p class="mb-0">إجمالي المستخدمين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <?php 
                $active_count = $db->query("SELECT COUNT(*) as count FROM users WHERE status = 'active' AND user_type != 'admin'")->fetch()['count'];
                ?>
                <h4><?php echo $active_count; ?></h4>
                <p class="mb-0">مستخدمين نشطين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <?php 
                $customers_count = $db->query("SELECT COUNT(*) as count FROM users WHERE user_type = 'customer'")->fetch()['count'];
                ?>
                <h4><?php echo $customers_count; ?></h4>
                <p class="mb-0">عملاء</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <?php 
                $vendors_count = $db->query("SELECT COUNT(*) as count FROM users WHERE user_type = 'vendor'")->fetch()['count'];
                ?>
                <h4><?php echo $vendors_count; ?></h4>
                <p class="mb-0">محلات</p>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" value="<?php echo htmlspecialchars($search); ?>" placeholder="اسم، إيميل، هاتف، أو اسم المحل">
            </div>
            <div class="col-md-3">
                <label class="form-label">نوع المستخدم</label>
                <select name="user_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    <option value="customer" <?php echo $user_type_filter === 'customer' ? 'selected' : ''; ?>>عملاء</option>
                    <option value="vendor" <?php echo $user_type_filter === 'vendor' ? 'selected' : ''; ?>>محلات</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>نشط</option>
                    <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                    <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>معلق</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- جدول المستخدمين -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة المستخدمين (<?php echo $total_users; ?>)</h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($users)): ?>
            <div class="text-center py-5">
                <i class="fas fa-users fs-1 text-muted mb-3"></i>
                <p class="text-muted">لا يوجد مستخدمين</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>المستخدم</th>
                            <th>النوع</th>
                            <th>معلومات الاتصال</th>
                            <th>الحالة</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($user['name']); ?></strong>
                                        <?php if ($user['business_name']): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($user['business_name']); ?></small>
                                        <?php endif; ?>
                                        <?php if ($user['city']): ?>
                                            <br><small class="text-muted"><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($user['city']); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $user['user_type'] === 'customer' ? 'primary' : 'success'; ?>">
                                        <?php echo $user['user_type'] === 'customer' ? 'عميل' : 'محل'; ?>
                                    </span>
                                    <?php if ($user['user_type'] === 'vendor' && $user['rating'] > 0): ?>
                                        <br><small class="text-warning">
                                            <i class="fas fa-star"></i> <?php echo number_format($user['rating'], 1); ?>
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="small">
                                        <div><i class="fas fa-envelope"></i> <?php echo htmlspecialchars($user['email']); ?></div>
                                        <div><i class="fas fa-phone"></i> <?php echo htmlspecialchars($user['phone']); ?></div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo $user['status'] === 'active' ? 'success' : 
                                            ($user['status'] === 'inactive' ? 'secondary' : 'warning'); 
                                    ?>">
                                        <?php 
                                        $status_text = [
                                            'active' => 'نشط',
                                            'inactive' => 'غير نشط',
                                            'suspended' => 'معلق'
                                        ];
                                        echo $status_text[$user['status']] ?? $user['status'];
                                        ?>
                                    </span>
                                    <?php if ($user['email_verified']): ?>
                                        <br><small class="text-success"><i class="fas fa-check-circle"></i> مؤكد</small>
                                    <?php else: ?>
                                        <br><small class="text-warning"><i class="fas fa-exclamation-circle"></i> غير مؤكد</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?php echo formatArabicDate($user['created_at']); ?></small>
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            إجراءات
                                        </button>
                                        <ul class="dropdown-menu">
                                            <?php if ($user['status'] !== 'active'): ?>
                                                <li>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                        <input type="hidden" name="action" value="activate">
                                                        <button type="submit" class="dropdown-item text-success">
                                                            <i class="fas fa-check me-1"></i> تفعيل
                                                        </button>
                                                    </form>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <?php if ($user['status'] === 'active'): ?>
                                                <li>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                        <input type="hidden" name="action" value="deactivate">
                                                        <button type="submit" class="dropdown-item text-warning">
                                                            <i class="fas fa-pause me-1"></i> إلغاء التفعيل
                                                        </button>
                                                    </form>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <?php if ($user['status'] !== 'suspended'): ?>
                                                <li>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                        <input type="hidden" name="action" value="suspend">
                                                        <button type="submit" class="dropdown-item text-warning" onclick="return confirm('هل أنت متأكد من تعليق هذا المستخدم؟')">
                                                            <i class="fas fa-ban me-1"></i> تعليق
                                                        </button>
                                                    </form>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <input type="hidden" name="action" value="delete">
                                                    <button type="submit" class="dropdown-item text-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                        <i class="fas fa-trash me-1"></i> حذف
                                                    </button>
                                                </form>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- ترقيم الصفحات -->
    <?php if ($total_pages > 1): ?>
        <div class="card-footer">
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&user_type=<?php echo $user_type_filter; ?>&status=<?php echo $status_filter; ?>">السابق</a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&user_type=<?php echo $user_type_filter; ?>&status=<?php echo $status_filter; ?>"><?php echo $i; ?></a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&user_type=<?php echo $user_type_filter; ?>&status=<?php echo $status_filter; ?>">التالي</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>
