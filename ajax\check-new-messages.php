<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['error' => 'غير مسجل الدخول']);
    exit;
}

$db = getDB();
$user_id = $_SESSION['user_id'];
$user_type = getUserType();

try {
    // جلب عدد الرسائل غير المقروءة
    $total_unread = getUnreadMessagesCount($user_id, $user_type);
    
    // جلب آخر رسالة
    $field = $user_type === 'customer' ? 'customer_id' : 'vendor_id';
    
    $last_message = $db->query(
        "SELECT m.created_at 
         FROM messages m
         JOIN conversations c ON m.conversation_id = c.id
         WHERE c.{$field} = ? AND m.sender_id != ?
         ORDER BY m.created_at DESC 
         LIMIT 1",
        [$user_id, $user_id]
    )->fetch();
    
    // التحقق من وجود رسائل جديدة
    $last_check = $_SESSION['last_message_check'] ?? 0;
    $has_new_messages = false;
    
    if ($last_message && strtotime($last_message['created_at']) > $last_check) {
        $has_new_messages = true;
        $_SESSION['last_message_check'] = time();
    }
    
    echo json_encode([
        'success' => true,
        'total_unread' => $total_unread,
        'has_new_messages' => $has_new_messages,
        'last_message_time' => $last_message ? $last_message['created_at'] : null
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => 'حدث خطأ أثناء فحص الرسائل',
        'details' => $e->getMessage()
    ]);
}
?>
