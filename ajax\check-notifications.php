<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['error' => 'غير مسجل الدخول']);
    exit;
}

$db = getDB();
$user_id = $_SESSION['user_id'];

try {
    // جلب عدد التنبيهات غير المقروءة
    $unread_count = getUnreadNotificationsCount($user_id);
    
    // جلب آخر تنبيه
    $last_notification = $db->query(
        "SELECT id, created_at FROM notifications 
         WHERE user_id = ? 
         ORDER BY created_at DESC 
         LIMIT 1",
        [$user_id]
    )->fetch();
    
    // التحقق من وجود تنبيهات جديدة
    $last_check = $_SESSION['last_notification_check'] ?? 0;
    $has_new_notifications = false;
    
    if ($last_notification && strtotime($last_notification['created_at']) > $last_check) {
        $has_new_notifications = true;
        $_SESSION['last_notification_check'] = time();
    }
    
    echo json_encode([
        'success' => true,
        'unread_count' => $unread_count,
        'has_new_notifications' => $has_new_notifications,
        'last_notification_time' => $last_notification ? $last_notification['created_at'] : null
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => 'حدث خطأ أثناء فحص التنبيهات',
        'details' => $e->getMessage()
    ]);
}
?>
