<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['error' => 'غير مسجل الدخول']);
    exit;
}

$conversation_id = intval($_GET['conversation_id'] ?? 0);

if (!$conversation_id) {
    echo json_encode(['error' => 'معرف المحادثة مطلوب']);
    exit;
}

$db = getDB();
$user_id = $_SESSION['user_id'];
$user_type = getUserType();

try {
    // التحقق من ملكية المحادثة
    $field = $user_type === 'customer' ? 'customer_id' : 'vendor_id';
    
    $conversation = $db->query(
        "SELECT id FROM conversations WHERE id = ? AND {$field} = ?",
        [$conversation_id, $user_id]
    )->fetch();
    
    if (!$conversation) {
        echo json_encode(['error' => 'المحادثة غير موجودة أو ليس لديك صلاحية']);
        exit;
    }
    
    // جلب الرسائل
    $messages = $db->query(
        "SELECT m.*, u.name as sender_name 
         FROM messages m
         JOIN users u ON m.sender_id = u.id
         WHERE m.conversation_id = ? AND m.is_deleted = FALSE
         ORDER BY m.created_at ASC",
        [$conversation_id]
    )->fetchAll();
    
    // تنسيق الرسائل
    $formatted_messages = [];
    foreach ($messages as $message) {
        $formatted_messages[] = [
            'id' => $message['id'],
            'content' => $message['content'],
            'sender_type' => $message['sender_type'],
            'sender_name' => $message['sender_name'],
            'created_at' => $message['created_at'],
            'is_read' => $message['is_read'],
            'formatted_date' => formatArabicDate($message['created_at'])
        ];
    }
    
    echo json_encode([
        'success' => true,
        'messages' => $formatted_messages,
        'total_count' => count($formatted_messages)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => 'حدث خطأ أثناء جلب الرسائل',
        'details' => $e->getMessage()
    ]);
}
?>
