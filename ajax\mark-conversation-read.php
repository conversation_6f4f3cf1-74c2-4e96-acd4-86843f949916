<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['error' => 'غير مسجل الدخول']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);
$conversation_id = intval($input['conversation_id'] ?? 0);
$user_type = $input['user_type'] ?? getUserType();

if (!$conversation_id) {
    echo json_encode(['error' => 'معرف المحادثة مطلوب']);
    exit;
}

$db = getDB();
$user_id = $_SESSION['user_id'];

try {
    // التحقق من ملكية المحادثة
    $field = $user_type === 'customer' ? 'customer_id' : 'vendor_id';
    
    $conversation = $db->query(
        "SELECT id FROM conversations WHERE id = ? AND {$field} = ?",
        [$conversation_id, $user_id]
    )->fetch();
    
    if (!$conversation) {
        echo json_encode(['error' => 'المحادثة غير موجودة أو ليس لديك صلاحية']);
        exit;
    }
    
    // تحديد الرسائل كمقروءة
    $sender_type = $user_type === 'customer' ? 'vendor' : 'customer';
    
    $db->query(
        "UPDATE messages SET is_read = TRUE, read_at = NOW() 
         WHERE conversation_id = ? AND sender_type = ? AND is_read = FALSE",
        [$conversation_id, $sender_type]
    );
    
    // تحديث عداد الرسائل غير المقروءة
    $unread_field = $user_type === 'customer' ? 'customer_unread_count' : 'vendor_unread_count';
    
    $db->query(
        "UPDATE conversations SET {$unread_field} = 0 WHERE id = ?",
        [$conversation_id]
    );
    
    echo json_encode([
        'success' => true,
        'message' => 'تم تحديث حالة القراءة بنجاح'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => 'حدث خطأ أثناء تحديث حالة القراءة',
        'details' => $e->getMessage()
    ]);
}
?>
