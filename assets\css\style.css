/* ملف الأنماط المخصص لموقع صناعية */

:root {
    --primary-color: #2c5aa0;
    --primary-dark: #1e3f73;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* الخطوط والنصوص */
body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
}

.fw-bold {
    font-weight: 700 !important;
}

/* الألوان المخصصة */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover,
.btn-primary:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover,
.btn-outline-primary:focus {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border-bottom: none;
    padding: 1.25rem 1.5rem;
}

/* شريط التنقل */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

/* النماذج */
.form-control,
.form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

/* التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    padding: 1rem 1.5rem;
}

.alert-dismissible .btn-close {
    padding: 1.25rem 1rem;
}

/* الشارات */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
    border-radius: 6px;
}

/* الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid var(--primary-color);
    font-weight: 600;
}

/* القوائم */
.list-group-item {
    border: none;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.25rem;
}

.list-group-item:last-child {
    border-bottom: none;
}

.list-group-item:hover {
    background-color: var(--light-color);
}

/* الأيقونات */
.feature-icon,
.step-number {
    width: 80px;
    height: 80px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-bottom: 1rem;
}

/* القسم الرئيسي */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

/* الإحصائيات */
.stats-section .stat-item {
    padding: 2rem 1rem;
}

.stats-section .display-4 {
    font-weight: 700;
    margin-bottom: 0.5rem;
}

/* التذييل */
footer {
    background-color: var(--dark-color) !important;
}

footer a {
    transition: color 0.3s ease;
}

footer a:hover {
    color: var(--primary-color) !important;
}

/* الرسوم المتحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0 !important;
    }
    
    .hero-section .display-4 {
        font-size: 2rem;
    }
    
    .stats-section .display-4 {
        font-size: 2.5rem;
    }
    
    .feature-icon,
    .step-number {
        width: 60px;
        height: 60px;
    }
    
    .card-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .btn-lg {
        padding: 0.5rem 1.5rem;
        font-size: 1rem;
    }
    
    .hero-section .lead {
        font-size: 1rem;
    }
}

/* حالات التحميل */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات إضافية */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.shadow-sm {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.shadow {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.shadow-lg {
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15) !important;
}

/* تخصيص شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}
