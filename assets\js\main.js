/**
 * ملف JavaScript الرئيسي لموقع صناعية
 * Main JavaScript file for Sanaeya website
 */

// تشغيل الكود عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التطبيق
    initializeApp();
    
    // إعداد معالجات الأحداث
    setupEventHandlers();
    
    // تهيئة المكونات
    initializeComponents();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // إخفاء التنبيهات تلقائياً بعد 5 ثوان
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            if (bootstrap.Alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        });
    }, 5000);
    
    // تحديث الوقت
    updateTime();
    setInterval(updateTime, 60000); // كل دقيقة
}

/**
 * إعداد معالجات الأحداث
 */
function setupEventHandlers() {
    // تأكيد الحذف
    document.querySelectorAll('[data-confirm-delete]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            const message = this.getAttribute('data-confirm-delete') || 'هل أنت متأكد من الحذف؟';
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        });
    });
    
    // تأكيد الإجراءات
    document.querySelectorAll('[data-confirm]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            const message = this.getAttribute('data-confirm') || 'هل أنت متأكد؟';
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        });
    });
    
    // معاينة الصور قبل الرفع
    document.querySelectorAll('input[type="file"][accept*="image"]').forEach(function(input) {
        input.addEventListener('change', function() {
            previewImages(this);
        });
    });
    
    // تحديد الكل في الجداول
    document.querySelectorAll('.select-all').forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            const targetClass = this.getAttribute('data-target') || '.select-item';
            const checkboxes = document.querySelectorAll(targetClass);
            checkboxes.forEach(function(cb) {
                cb.checked = checkbox.checked;
            });
        });
    });
}

/**
 * تهيئة المكونات
 */
function initializeComponents() {
    // تهيئة التلميحات
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // تهيئة النوافذ المنبثقة
    if (typeof bootstrap !== 'undefined' && bootstrap.Popover) {
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function(popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }
    
    // تهيئة العدادات المتحركة
    animateCounters();
    
    // تهيئة التمرير السلس
    initializeSmoothScroll();
}

/**
 * تحديث الوقت
 */
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    const timeElements = document.querySelectorAll('.current-time');
    timeElements.forEach(function(element) {
        element.textContent = timeString;
    });
}

/**
 * معاينة الصور
 */
function previewImages(input) {
    const previewContainer = document.getElementById('image-preview') || createPreviewContainer(input);
    previewContainer.innerHTML = '';
    
    if (input.files) {
        Array.from(input.files).forEach(function(file, index) {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'img-thumbnail me-2 mb-2';
                    img.style.width = '100px';
                    img.style.height = '100px';
                    img.style.objectFit = 'cover';
                    previewContainer.appendChild(img);
                };
                reader.readAsDataURL(file);
            }
        });
    }
}

/**
 * إنشاء حاوي معاينة الصور
 */
function createPreviewContainer(input) {
    const container = document.createElement('div');
    container.id = 'image-preview';
    container.className = 'mt-2';
    input.parentNode.appendChild(container);
    return container;
}

/**
 * تحريك العدادات
 */
function animateCounters() {
    const counters = document.querySelectorAll('.counter');
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(function(entry) {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });
    
    counters.forEach(function(counter) {
        observer.observe(counter);
    });
}

/**
 * تحريك عداد واحد
 */
function animateCounter(element) {
    const target = parseInt(element.getAttribute('data-target') || element.textContent);
    const duration = parseInt(element.getAttribute('data-duration')) || 2000;
    const increment = target / (duration / 16);
    let current = 0;
    
    const timer = setInterval(function() {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current).toLocaleString('ar-SA');
    }, 16);
}

/**
 * التمرير السلس
 */
function initializeSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(function(anchor) {
        anchor.addEventListener('click', function(e) {
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * إظهار حالة التحميل
 */
function showLoading(element) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    if (element) {
        element.classList.add('loading');
        element.disabled = true;
    }
}

/**
 * إخفاء حالة التحميل
 */
function hideLoading(element) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    if (element) {
        element.classList.remove('loading');
        element.disabled = false;
    }
}

/**
 * إظهار تنبيه
 */
function showAlert(message, type = 'info', duration = 5000) {
    const alertContainer = document.getElementById('alert-container') || createAlertContainer();
    
    const alertElement = document.createElement('div');
    alertElement.className = `alert alert-${type} alert-dismissible fade show`;
    alertElement.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alertElement);
    
    // إزالة التنبيه تلقائياً
    if (duration > 0) {
        setTimeout(function() {
            if (alertElement.parentNode) {
                const bsAlert = new bootstrap.Alert(alertElement);
                bsAlert.close();
            }
        }, duration);
    }
}

/**
 * إنشاء حاوي التنبيهات
 */
function createAlertContainer() {
    const container = document.createElement('div');
    container.id = 'alert-container';
    container.className = 'position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}

/**
 * تنسيق الأرقام
 */
function formatNumber(number, decimals = 0) {
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(number);
}

/**
 * تنسيق العملة
 */
function formatCurrency(amount, currency = 'SAR') {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

/**
 * تنسيق التاريخ
 */
function formatDate(date, options = {}) {
    const defaultOptions = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    
    return new Intl.DateTimeFormat('ar-SA', { ...defaultOptions, ...options }).format(new Date(date));
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * التحقق من صحة رقم الهاتف السعودي
 */
function isValidSaudiPhone(phone) {
    const phoneRegex = /^(05|5)[0-9]{8}$/;
    return phoneRegex.test(phone.replace(/\s+/g, ''));
}

// تحديث التنبيهات والرسائل
setInterval(function() {
    updateNotificationCount();
}, 60000);

setInterval(function() {
    updateMessageCount();
}, 30000);

function updateNotificationCount() {
    fetch('ajax/check-notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateBadge('.notification-badge', data.unread_count);

                if (data.has_new_notifications) {
                    showToast('لديك تنبيهات جديدة', 'info');
                }
            }
        })
        .catch(error => console.error('Error updating notifications:', error));
}

function updateMessageCount() {
    fetch('ajax/check-new-messages.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateBadge('.message-badge', data.total_unread);

                if (data.has_new_messages) {
                    showToast('لديك رسائل جديدة', 'primary');
                }
            }
        })
        .catch(error => console.error('Error updating messages:', error));
}

function updateBadge(selector, count) {
    const badges = document.querySelectorAll(selector);
    badges.forEach(badge => {
        if (count > 0) {
            badge.textContent = count > 99 ? '99+' : count;
            badge.style.display = 'flex';
        } else {
            badge.style.display = 'none';
        }
    });
}

// دالة إظهار التوست
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    toastContainer.appendChild(toast);

    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

/**
 * تصدير الدوال للاستخدام العام
 */
window.SanaeyaApp = {
    showLoading,
    hideLoading,
    showAlert,
    showToast,
    formatNumber,
    formatCurrency,
    formatDate,
    isValidEmail,
    isValidSaudiPhone,
    updateNotificationCount,
    updateMessageCount
};
