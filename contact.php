<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

$errors = [];
$success = false;

if ($_POST) {
    $name = sanitize($_POST['name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $phone = sanitize($_POST['phone'] ?? '');
    $subject = sanitize($_POST['subject'] ?? '');
    $message = sanitize($_POST['message'] ?? '');
    
    // التحقق من البيانات
    if (empty($name)) {
        $errors[] = 'الاسم مطلوب';
    }
    
    if (empty($email) || !isValidEmail($email)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }
    
    if (empty($phone)) {
        $errors[] = 'رقم الهاتف مطلوب';
    }
    
    if (empty($subject)) {
        $errors[] = 'الموضوع مطلوب';
    }
    
    if (empty($message)) {
        $errors[] = 'الرسالة مطلوبة';
    }
    
    // إرسال الرسالة (محاكاة)
    if (empty($errors)) {
        // هنا يمكن إضافة كود إرسال الإيميل الفعلي
        // أو حفظ الرسالة في قاعدة البيانات
        
        $success = true;
        showAlert('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.', 'success');
        
        // إعادة تعيين النموذج
        $_POST = [];
    }
}

$page_title = 'اتصل بنا';
include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-5 mb-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-4">اتصل بنا</h1>
                <p class="lead">نحن هنا لمساعدتك! تواصل معنا في أي وقت</p>
            </div>
        </div>
    </div>
</section>

<div class="container">
    <div class="row">
        <!-- نموذج الاتصال -->
        <div class="col-lg-8 mb-5">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-envelope me-2"></i>
                        أرسل لنا رسالة
                    </h4>
                </div>
                <div class="card-body p-4">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                        <div class="alert alert-success text-center">
                            <i class="fas fa-check-circle fs-2 mb-3"></i>
                            <h5>تم إرسال رسالتك بنجاح!</h5>
                            <p class="mb-0">شكراً لتواصلك معنا. سنرد عليك في أقرب وقت ممكن.</p>
                        </div>
                    <?php else: ?>
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاسم الكامل *</label>
                                    <input type="text" name="name" class="form-control" value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">البريد الإلكتروني *</label>
                                    <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الهاتف *</label>
                                    <input type="tel" name="phone" class="form-control" value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الموضوع *</label>
                                    <select name="subject" class="form-select" required>
                                        <option value="">اختر الموضوع</option>
                                        <option value="استفسار عام" <?php echo ($_POST['subject'] ?? '') === 'استفسار عام' ? 'selected' : ''; ?>>استفسار عام</option>
                                        <option value="مشكلة تقنية" <?php echo ($_POST['subject'] ?? '') === 'مشكلة تقنية' ? 'selected' : ''; ?>>مشكلة تقنية</option>
                                        <option value="شكوى" <?php echo ($_POST['subject'] ?? '') === 'شكوى' ? 'selected' : ''; ?>>شكوى</option>
                                        <option value="اقتراح" <?php echo ($_POST['subject'] ?? '') === 'اقتراح' ? 'selected' : ''; ?>>اقتراح</option>
                                        <option value="طلب شراكة" <?php echo ($_POST['subject'] ?? '') === 'طلب شراكة' ? 'selected' : ''; ?>>طلب شراكة</option>
                                        <option value="أخرى" <?php echo ($_POST['subject'] ?? '') === 'أخرى' ? 'selected' : ''; ?>>أخرى</option>
                                    </select>
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label class="form-label">الرسالة *</label>
                                    <textarea name="message" class="form-control" rows="5" placeholder="اكتب رسالتك هنا..." required><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg px-5">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    إرسال الرسالة
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- معلومات الاتصال -->
        <div class="col-lg-4">
            <!-- معلومات التواصل -->
            <div class="card shadow mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-phone me-2"></i>
                        معلومات التواصل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div>
                            <strong>الهاتف</strong>
                            <br><a href="tel:+966501234567" class="text-decoration-none">+966 50 123 4567</a>
                        </div>
                    </div>
                    
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div>
                            <strong>البريد الإلكتروني</strong>
                            <br><a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                        </div>
                    </div>
                    
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div>
                            <strong>العنوان</strong>
                            <br>الرياض، المملكة العربية السعودية
                        </div>
                    </div>
                    
                    <div class="d-flex align-items-center">
                        <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div>
                            <strong>ساعات العمل</strong>
                            <br>24/7 - على مدار الساعة
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- وسائل التواصل الاجتماعي -->
            <div class="card shadow mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-share-alt me-2"></i>
                        تابعنا على
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="d-flex justify-content-center gap-3">
                        <a href="#" class="btn btn-outline-primary btn-lg">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="btn btn-outline-primary btn-lg">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" class="btn btn-outline-success btn-lg">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                        <a href="#" class="btn btn-outline-danger btn-lg">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                    <p class="text-muted mt-3 mb-0">تابعنا للحصول على آخر الأخبار والعروض</p>
                </div>
            </div>
            
            <!-- الأسئلة الشائعة -->
            <div class="card shadow">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        الأسئلة الشائعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq1">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                    كيف أطلب قطعة غيار؟
                                </button>
                            </h2>
                            <div id="collapse1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    قم بالتسجيل في الموقع، ثم اذهب إلى "طلب قطعة غيار" واملأ البيانات المطلوبة.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                    كم يستغرق الحصول على عروض؟
                                </button>
                            </h2>
                            <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    عادة ما تصل العروض خلال دقائق من إرسال الطلب، وقد تستغرق حتى 24 ساعة في بعض الحالات.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq3">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                    هل الخدمة مجانية؟
                                </button>
                            </h2>
                            <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    نعم، الخدمة مجانية تماماً للعملاء. لا توجد أي رسوم على طلب قطع الغيار أو استقبال العروض.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- خريطة (اختيارية) -->
    <section class="mt-5">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-map me-2"></i>
                    موقعنا
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="bg-light text-center py-5">
                    <i class="fas fa-map-marked-alt fs-1 text-muted mb-3"></i>
                    <h5 class="text-muted">خريطة الموقع</h5>
                    <p class="text-muted">الرياض، المملكة العربية السعودية</p>
                    <!-- هنا يمكن إضافة خريطة Google Maps -->
                </div>
            </div>
        </div>
    </section>
</div>

<?php include 'includes/footer.php'; ?>
