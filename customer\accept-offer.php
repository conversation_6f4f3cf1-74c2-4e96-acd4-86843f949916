<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'customer') {
    showAlert('يجب تسجيل الدخول كعميل للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();
$customer_id = $_SESSION['user_id'];
$offer_id = intval($_GET['id'] ?? 0);

if (!$offer_id) {
    showAlert('عرض غير صحيح', 'error');
    redirect('offers.php');
}

// جلب تفاصيل العرض
$offer = $db->query(
    "SELECT o.*, pr.part_name, pr.customer_id, v.name as vendor_name
     FROM offers o 
     JOIN part_requests pr ON o.request_id = pr.id 
     JOIN users v ON o.vendor_id = v.id
     WHERE o.id = ? AND pr.customer_id = ? AND o.status = 'pending'",
    [$offer_id, $customer_id]
)->fetch();

if (!$offer) {
    showAlert('العرض غير موجود أو تم قبوله/رفضه مسبقاً', 'error');
    redirect('offers.php');
}

// معالجة قبول العرض
if ($_POST && isset($_POST['confirm_accept'])) {
    try {
        $db->beginTransaction();
        
        // تحديث حالة العرض إلى مقبول
        $db->query(
            "UPDATE offers SET status = 'accepted', accepted_at = NOW() WHERE id = ?",
            [$offer_id]
        );
        
        // رفض جميع العروض الأخرى لنفس الطلب
        $db->query(
            "UPDATE offers SET status = 'rejected' 
             WHERE request_id = ? AND id != ? AND status = 'pending'",
            [$offer['request_id'], $offer_id]
        );
        
        // تحديث حالة الطلب إلى مكتمل
        $db->query(
            "UPDATE part_requests SET status = 'completed' WHERE id = ?",
            [$offer['request_id']]
        );
        
        // إنشاء معاملة مالية
        $db->query(
            "INSERT INTO transactions (customer_id, vendor_id, offer_id, amount, status)
             VALUES (?, ?, ?, ?, 'pending')",
            [$customer_id, $offer['vendor_id'], $offer_id, $offer['price']]
        );

        $transaction_id = $db->getConnection()->lastInsertId();
        
        // إنشاء عمولة
        createCommission($transaction_id, $offer['vendor_id'], $customer_id, $offer_id, $offer['price']);
        
        // إرسال رسالة في المحادثة
        $conversation_id = createOrGetConversation($offer_id, $customer_id, $offer['vendor_id']);
        $accept_message = "رائع! لقد قبلت عرضك. يرجى التواصل معي لترتيب التسليم والدفع.";
        sendMessage($conversation_id, $customer_id, 'customer', $accept_message, 'system');
        
        // إنشاء تنبيه للمحل
        createNotification(
            $offer['vendor_id'],
            'offer_accepted',
            'تم قبول عرضك!',
            "تهانينا! العميل {$_SESSION['user_name']} قبل عرضك لقطعة {$offer['part_name']} بسعر " . formatPrice($offer['price']),
            [
                'offer_id' => $offer_id,
                'customer_name' => $_SESSION['user_name'],
                'part_name' => $offer['part_name'],
                'price' => $offer['price']
            ],
            "vendor/offer-details.php?id={$offer_id}",
            'high'
        );
        
        // إنشاء تنبيه عمولة للمحل
        createNotification(
            $offer['vendor_id'],
            'commission_due',
            'عمولة مستحقة',
            "لديك عمولة مستحقة بقيمة " . formatPrice($offer['price'] * 0.03) . " من صفقة {$offer['part_name']}",
            ['transaction_id' => $transaction_id],
            "vendor/commissions.php"
        );
        
        // إنشاء تنبيه عمولة للعميل
        createNotification(
            $customer_id,
            'commission_due',
            'عمولة خدمة مستحقة',
            "لديك عمولة خدمة مستحقة بقيمة " . formatPrice($offer['price'] * 0.03) . " من صفقة {$offer['part_name']}",
            ['transaction_id' => $transaction_id],
            "customer/commissions.php"
        );
        
        $db->commit();
        
        showAlert('تم قبول العرض بنجاح! تم إشعار المحل وإنشاء المعاملة المالية.', 'success');
        redirect("chat.php?conversation_id={$conversation_id}");
        
    } catch (Exception $e) {
        $db->rollback();
        showAlert('حدث خطأ أثناء قبول العرض: ' . $e->getMessage(), 'error');
    }
}

$page_title = 'قبول العرض';
include '../includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-success text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    تأكيد قبول العرض
                </h4>
            </div>
            <div class="card-body p-4">
                
                <!-- تفاصيل العرض -->
                <div class="alert alert-info">
                    <h5 class="mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        تفاصيل العرض المراد قبوله
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>القطعة:</strong> <?php echo htmlspecialchars($offer['part_name']); ?></p>
                            <p><strong>المحل:</strong> <?php echo htmlspecialchars($offer['vendor_name']); ?></p>
                            <p><strong>السعر:</strong> <span class="text-success fw-bold fs-4"><?php echo formatPrice($offer['price']); ?></span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>حالة القطعة:</strong> 
                                <span class="badge bg-<?php echo $offer['condition_type'] === 'new' ? 'success' : 'warning'; ?>">
                                    <?php echo $offer['condition_type'] === 'new' ? 'جديدة' : 'مستعملة'; ?>
                                </span>
                            </p>
                            <p><strong>فترة الضمان:</strong> <?php echo $offer['warranty_period'] > 0 ? $offer['warranty_period'] . ' شهر' : 'بدون ضمان'; ?></p>
                            <p><strong>تكلفة التوصيل:</strong> <?php echo $offer['delivery_cost'] > 0 ? formatPrice($offer['delivery_cost']) : 'مجاني'; ?></p>
                        </div>
                    </div>
                </div>
                
                <!-- تنبيه مهم -->
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه مهم:</h6>
                    <ul class="mb-0">
                        <li>بقبولك لهذا العرض، سيتم رفض جميع العروض الأخرى تلقائياً</li>
                        <li>ستصبح مسؤولاً عن دفع المبلغ المتفق عليه</li>
                        <li>ستكون هناك عمولة خدمة بنسبة 3% من قيمة الصفقة</li>
                        <li>يجب التواصل مع المحل لترتيب التسليم والدفع</li>
                    </ul>
                </div>
                
                <!-- ملخص التكاليف -->
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">ملخص التكاليف:</h6>
                        <div class="row">
                            <div class="col-6">
                                <p class="mb-1">سعر القطعة:</p>
                                <p class="mb-1">تكلفة التوصيل:</p>
                                <p class="mb-1">عمولة الخدمة (3%):</p>
                                <hr>
                                <p class="mb-0 fw-bold">إجمالي التكلفة:</p>
                            </div>
                            <div class="col-6 text-end">
                                <p class="mb-1"><?php echo formatPrice($offer['price']); ?></p>
                                <p class="mb-1"><?php echo $offer['delivery_cost'] > 0 ? formatPrice($offer['delivery_cost']) : 'مجاني'; ?></p>
                                <p class="mb-1 text-warning"><?php echo formatPrice($offer['price'] * 0.03); ?></p>
                                <hr>
                                <p class="mb-0 fw-bold text-success fs-5">
                                    <?php echo formatPrice($offer['price'] + $offer['delivery_cost'] + ($offer['price'] * 0.03)); ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- نموذج التأكيد -->
                <form method="POST" class="mt-4">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="agree_terms" required>
                        <label class="form-check-label" for="agree_terms">
                            أوافق على <a href="../terms.php" target="_blank">شروط الاستخدام</a> وأتحمل مسؤولية هذه الصفقة
                        </label>
                    </div>
                    
                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="understand_commission" required>
                        <label class="form-check-label" for="understand_commission">
                            أفهم أن هناك عمولة خدمة بنسبة 3% من قيمة الصفقة
                        </label>
                    </div>
                    
                    <div class="d-flex gap-3 justify-content-center">
                        <button type="submit" name="confirm_accept" class="btn btn-success btn-lg px-5">
                            <i class="fas fa-check me-2"></i>
                            تأكيد قبول العرض
                        </button>
                        
                        <a href="offers.php?request_id=<?php echo $offer['request_id']; ?>" class="btn btn-outline-secondary btn-lg px-5">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للعروض
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// تأكيد إضافي قبل الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    if (!confirm('هل أنت متأكد من قبول هذا العرض؟ لا يمكن التراجع عن هذا الإجراء.')) {
        e.preventDefault();
    }
});
</script>

<?php include '../includes/footer.php'; ?>
