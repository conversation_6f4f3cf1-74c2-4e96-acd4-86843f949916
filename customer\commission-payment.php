<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'customer') {
    showAlert('يجب تسجيل الدخول كعميل للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();
$customer_id = $_SESSION['user_id'];

$errors = [];
$success = false;

// جلب العمولة المحددة إذا تم تمرير معرف
$commission_id = intval($_GET['commission_id'] ?? 0);
$selected_commission = null;

if ($commission_id) {
    $selected_commission = $db->query(
        "SELECT c.*, pr.part_name, pr.car_make, pr.car_model, pr.car_year, 
         v.name as vendor_name, vp.business_name
         FROM commissions c
         JOIN offers o ON c.offer_id = o.id
         JOIN part_requests pr ON o.request_id = pr.id
         JOIN users v ON c.vendor_id = v.id
         LEFT JOIN vendor_profiles vp ON v.id = vp.user_id
         WHERE c.id = ? AND c.customer_id = ? AND c.customer_payment_status != 'paid'",
        [$commission_id, $customer_id]
    )->fetch();
}

// جلب العمولات غير المدفوعة
$unpaid_commissions = $db->query(
    "SELECT c.*, pr.part_name, pr.car_make, pr.car_model, pr.car_year, 
     v.name as vendor_name, vp.business_name
     FROM commissions c
     JOIN offers o ON c.offer_id = o.id
     JOIN part_requests pr ON o.request_id = pr.id
     JOIN users v ON c.vendor_id = v.id
     LEFT JOIN vendor_profiles vp ON v.id = vp.user_id
     WHERE c.customer_id = ? AND c.customer_payment_status IN ('pending', 'overdue')
     ORDER BY c.due_date ASC",
    [$customer_id]
)->fetchAll();

// معالجة دفع العمولة
if ($_POST) {
    $commission_ids = $_POST['commission_ids'] ?? [];
    $payment_method = sanitize($_POST['payment_method'] ?? '');
    $payment_reference = sanitize($_POST['payment_reference'] ?? '');
    $notes = sanitize($_POST['notes'] ?? '');
    
    // التحقق من البيانات
    if (empty($commission_ids)) {
        $errors[] = 'يجب اختيار عمولة واحدة على الأقل للدفع';
    }
    
    if (empty($payment_method)) {
        $errors[] = 'طريقة الدفع مطلوبة';
    }
    
    // معالجة رفع إيصال الدفع
    $receipt_filename = null;
    if (isset($_FILES['receipt']) && $_FILES['receipt']['error'] === UPLOAD_ERR_OK) {
        if (!is_dir('../uploads/receipts/')) {
            mkdir('../uploads/receipts/', 0755, true);
        }
        
        $receipt_filename = uploadFile($_FILES['receipt'], '../uploads/receipts/');
        if (!$receipt_filename) {
            $errors[] = 'فشل في رفع إيصال الدفع';
        }
    }
    
    // تسجيل الدفعات
    if (empty($errors)) {
        $db->beginTransaction();
        
        try {
            $total_paid = 0;
            
            foreach ($commission_ids as $comm_id) {
                $comm_id = intval($comm_id);
                
                // التحقق من ملكية العمولة
                $commission = $db->query(
                    "SELECT * FROM commissions WHERE id = ? AND customer_id = ? AND customer_payment_status != 'paid'",
                    [$comm_id, $customer_id]
                )->fetch();
                
                if ($commission) {
                    // تسجيل الدفعة
                    $db->query(
                        "INSERT INTO commission_payments (commission_id, payer_id, payer_type, amount, payment_method, payment_reference, notes, receipt_image) VALUES (?, ?, 'customer', ?, ?, ?, ?, ?)",
                        [
                            $comm_id,
                            $customer_id,
                            $commission['customer_commission_amount'],
                            $payment_method,
                            $payment_reference,
                            $notes,
                            $receipt_filename
                        ]
                    );
                    
                    // تحديث حالة العمولة
                    $db->query(
                        "UPDATE commissions SET customer_payment_status = 'paid', customer_paid_at = NOW() WHERE id = ?",
                        [$comm_id]
                    );
                    
                    // تحديث الحالة العامة إذا دفع المحل أيضاً
                    $db->query(
                        "UPDATE commissions SET overall_status = CASE 
                         WHEN vendor_payment_status = 'paid' THEN 'completed'
                         ELSE 'partial'
                         END WHERE id = ?",
                        [$comm_id]
                    );
                    
                    $total_paid += $commission['customer_commission_amount'];
                }
            }
            
            $db->commit();
            $success = true;
            showAlert("تم تسجيل دفع العمولات بنجاح! إجمالي المبلغ: " . formatPrice($total_paid), 'success');
            redirect('commissions.php');
            
        } catch (Exception $e) {
            $db->rollback();
            $errors[] = 'حدث خطأ أثناء تسجيل الدفعة: ' . $e->getMessage();
        }
    }
}

$page_title = 'دفع عمولة الخدمة';
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold text-success">
        <i class="fas fa-credit-card me-2"></i>
        دفع عمولة الخدمة
    </h2>
    <div class="d-flex gap-2">
        <a href="commissions.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للعمولات
        </a>
    </div>
</div>

<?php if (empty($unpaid_commissions)): ?>
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-check-circle fs-1 text-success mb-3"></i>
            <h4 class="text-success">ممتاز!</h4>
            <p class="text-muted">جميع عمولاتك مدفوعة. لا توجد عمولات مستحقة حالياً.</p>
            <div class="d-flex justify-content-center gap-2">
                <a href="commissions.php" class="btn btn-primary">
                    <i class="fas fa-list me-2"></i>
                    عرض جميع العمولات
                </a>
                <a href="../request-part.php" class="btn btn-outline-primary">
                    <i class="fas fa-plus me-2"></i>
                    اطلب قطعة غيار جديدة
                </a>
            </div>
        </div>
    </div>
<?php else: ?>
    <!-- شرح عمولة الخدمة -->
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-start">
            <i class="fas fa-lightbulb fs-4 me-3 mt-1"></i>
            <div>
                <h6 class="mb-2">لماذا عمولة الخدمة؟</h6>
                <p class="mb-0">
                    عمولة الخدمة البسيطة (3%) تساعدنا في توفير منصة آمنة وموثوقة لربطك بأفضل المحلات، 
                    وضمان جودة الخدمة، وتطوير ميزات جديدة لتحسين تجربتك في العثور على قطع الغيار.
                </p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- نموذج الدفع -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        تسجيل دفعة عمولة
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="POST" enctype="multipart/form-data">
                        <!-- اختيار العمولات -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">العمولات المراد دفعها *</label>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="50">
                                                <input type="checkbox" id="select_all" class="form-check-input">
                                            </th>
                                            <th>الصفقة</th>
                                            <th>المحل</th>
                                            <th>المبلغ</th>
                                            <th>الاستحقاق</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($unpaid_commissions as $commission): ?>
                                            <tr>
                                                <td>
                                                    <input type="checkbox" name="commission_ids[]" value="<?php echo $commission['id']; ?>" 
                                                           class="form-check-input commission-checkbox"
                                                           <?php echo ($selected_commission && $selected_commission['id'] == $commission['id']) ? 'checked' : ''; ?>>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($commission['part_name']); ?></strong>
                                                        <br><small class="text-muted">
                                                            <?php echo htmlspecialchars($commission['car_make'] . ' ' . $commission['car_model']); ?>
                                                        </small>
                                                    </div>
                                                </td>
                                                <td><?php echo htmlspecialchars($commission['business_name'] ?: $commission['vendor_name']); ?></td>
                                                <td>
                                                    <span class="fw-bold text-primary">
                                                        <?php echo formatPrice($commission['customer_commission_amount']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="<?php echo strtotime($commission['due_date']) < time() ? 'text-danger' : 'text-success'; ?>">
                                                        <?php echo formatArabicDate($commission['due_date']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo $commission['customer_payment_status'] === 'overdue' ? 'danger' : 'warning'; ?>">
                                                        <?php echo $commission['customer_payment_status'] === 'overdue' ? 'متأخرة' : 'في الانتظار'; ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- تفاصيل الدفع -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">طريقة الدفع *</label>
                                <select name="payment_method" class="form-select" required>
                                    <option value="">اختر طريقة الدفع</option>
                                    <option value="bank_transfer">تحويل بنكي</option>
                                    <option value="online">دفع إلكتروني</option>
                                    <option value="credit_card">بطاقة ائتمان</option>
                                    <option value="cash">نقداً</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم المرجع / الإيصال</label>
                                <input type="text" name="payment_reference" class="form-control" placeholder="رقم التحويل أو الإيصال">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">إيصال الدفع (صورة)</label>
                            <input type="file" name="receipt" class="form-control" accept="image/*">
                            <div class="form-text">يمكنك رفع صورة إيصال الدفع للتحقق</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات إضافية</label>
                            <textarea name="notes" class="form-control" rows="3" placeholder="أي ملاحظات حول الدفعة"></textarea>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-success btn-lg px-5">
                                <i class="fas fa-credit-card me-2"></i>
                                تسجيل الدفعة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- ملخص الدفع -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        ملخص الدفع
                    </h6>
                </div>
                <div class="card-body">
                    <div id="payment-summary">
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-hand-pointer fs-2 mb-2"></i>
                            <p>اختر العمولات المراد دفعها</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- طرق الدفع المتاحة -->
            <div class="card mt-3">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>
                        طرق الدفع المتاحة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <i class="fas fa-university fs-2 text-primary mb-2"></i>
                            <p class="small mb-0">تحويل بنكي</p>
                        </div>
                        <div class="col-6 mb-3">
                            <i class="fas fa-globe fs-2 text-success mb-2"></i>
                            <p class="small mb-0">دفع إلكتروني</p>
                        </div>
                        <div class="col-6 mb-3">
                            <i class="fas fa-credit-card fs-2 text-warning mb-2"></i>
                            <p class="small mb-0">بطاقة ائتمان</p>
                        </div>
                        <div class="col-6 mb-3">
                            <i class="fas fa-money-bill fs-2 text-info mb-2"></i>
                            <p class="small mb-0">نقداً</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات مهمة -->
            <div class="card mt-3">
                <div class="card-header bg-warning text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات مهمة
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            العمولة 3% من قيمة كل صفقة
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-calendar text-warning me-2"></i>
                            مدة السداد 30 يوم من تاريخ الصفقة
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-shield-alt text-primary me-2"></i>
                            جميع المدفوعات محمية ومؤمنة
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-headset text-info me-2"></i>
                            دعم فني متاح 24/7
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('select_all');
    const commissionCheckboxes = document.querySelectorAll('.commission-checkbox');
    const paymentSummary = document.getElementById('payment-summary');
    
    // تحديد/إلغاء تحديد الكل
    selectAllCheckbox.addEventListener('change', function() {
        commissionCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updatePaymentSummary();
    });
    
    // تحديث الملخص عند تغيير التحديد
    commissionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updatePaymentSummary);
    });
    
    function updatePaymentSummary() {
        const selectedCheckboxes = document.querySelectorAll('.commission-checkbox:checked');
        
        if (selectedCheckboxes.length === 0) {
            paymentSummary.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="fas fa-hand-pointer fs-2 mb-2"></i>
                    <p>اختر العمولات المراد دفعها</p>
                </div>
            `;
            return;
        }
        
        let totalAmount = 0;
        let itemsHtml = '';
        
        selectedCheckboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            const partName = row.cells[1].querySelector('strong').textContent;
            const amount = row.cells[3].querySelector('.fw-bold').textContent;
            const amountValue = parseFloat(amount.replace(/[^\d.]/g, ''));
            
            totalAmount += amountValue;
            itemsHtml += `
                <div class="d-flex justify-content-between mb-2">
                    <small>${partName}</small>
                    <small class="fw-bold">${amount}</small>
                </div>
            `;
        });
        
        paymentSummary.innerHTML = `
            <div>
                <h6 class="mb-3">العمولات المحددة (${selectedCheckboxes.length})</h6>
                ${itemsHtml}
                <hr>
                <div class="d-flex justify-content-between">
                    <strong>الإجمالي:</strong>
                    <strong class="text-success">${totalAmount.toLocaleString('ar-SA')} ريال</strong>
                </div>
                <div class="mt-3 text-center">
                    <small class="text-muted">
                        <i class="fas fa-heart text-danger me-1"></i>
                        شكراً لدعمك لتطوير المنصة
                    </small>
                </div>
            </div>
        `;
    }
    
    // تحديث الملخص عند تحميل الصفحة
    updatePaymentSummary();
});
</script>

<?php include '../includes/footer.php'; ?>
