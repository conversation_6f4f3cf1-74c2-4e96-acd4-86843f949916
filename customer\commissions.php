<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'customer') {
    showAlert('يجب تسجيل الدخول كعميل للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();
$customer_id = $_SESSION['user_id'];

// فلترة العمولات
$status_filter = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

// بناء الاستعلام
$where_conditions = ["c.customer_id = ?"];
$params = [$customer_id];

if (!empty($status_filter)) {
    $where_conditions[] = "c.customer_payment_status = ?";
    $params[] = $status_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// عدد العمولات الإجمالي
$count_query = "SELECT COUNT(*) as total FROM commissions c WHERE $where_clause";
$total_commissions = $db->query($count_query, $params)->fetch()['total'];
$total_pages = ceil($total_commissions / $per_page);

// جلب العمولات
$commissions_query = "SELECT c.*, 
                      pr.part_name, pr.car_make, pr.car_model, pr.car_year,
                      v.name as vendor_name, vp.business_name,
                      o.price as offer_price
                      FROM commissions c
                      JOIN offers o ON c.offer_id = o.id
                      JOIN part_requests pr ON o.request_id = pr.id
                      JOIN users v ON c.vendor_id = v.id
                      LEFT JOIN vendor_profiles vp ON v.id = vp.user_id
                      WHERE $where_clause 
                      ORDER BY c.created_at DESC 
                      LIMIT $per_page OFFSET $offset";

$commissions = $db->query($commissions_query, $params)->fetchAll();

// إحصائيات العمولات
$stats = [];
$stats['total'] = $db->query("SELECT COUNT(*) as count FROM commissions WHERE customer_id = ?", [$customer_id])->fetch()['count'];
$stats['pending'] = $db->query("SELECT COUNT(*) as count FROM commissions WHERE customer_id = ? AND customer_payment_status = 'pending'", [$customer_id])->fetch()['count'];
$stats['overdue'] = $db->query("SELECT COUNT(*) as count FROM commissions WHERE customer_id = ? AND customer_payment_status = 'overdue'", [$customer_id])->fetch()['count'];
$stats['paid'] = $db->query("SELECT COUNT(*) as count FROM commissions WHERE customer_id = ? AND customer_payment_status = 'paid'", [$customer_id])->fetch()['count'];

// إجمالي المبالغ
$amounts = $db->query("SELECT 
    COALESCE(SUM(CASE WHEN customer_payment_status = 'pending' THEN customer_commission_amount ELSE 0 END), 0) as pending_amount,
    COALESCE(SUM(CASE WHEN customer_payment_status = 'overdue' THEN customer_commission_amount ELSE 0 END), 0) as overdue_amount,
    COALESCE(SUM(CASE WHEN customer_payment_status = 'paid' THEN customer_commission_amount ELSE 0 END), 0) as paid_amount,
    COALESCE(SUM(customer_commission_amount), 0) as total_amount
    FROM commissions WHERE customer_id = ?", [$customer_id])->fetch();

$page_title = 'عمولات الخدمة';
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold text-primary">
        <i class="fas fa-percentage me-2"></i>
        عمولات الخدمة
    </h2>
    <div class="d-flex gap-2">
        <a href="commission-payment.php" class="btn btn-success">
            <i class="fas fa-credit-card me-1"></i>
            دفع عمولة
        </a>
        <a href="dashboard.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            لوحة التحكم
        </a>
    </div>
</div>

<!-- شرح نظام العمولات -->
<div class="alert alert-info">
    <div class="d-flex align-items-start">
        <i class="fas fa-info-circle fs-4 me-3 mt-1"></i>
        <div>
            <h6 class="mb-2">ما هي عمولة الخدمة؟</h6>
            <p class="mb-2">
                عمولة الخدمة هي رسم بسيط نسبته <strong>3%</strong> من قيمة كل صفقة ناجحة تتم عبر منصتنا. 
                هذه العمولة تساعدنا في تطوير وتحسين الخدمة المقدمة لك.
            </p>
            <ul class="mb-0">
                <li>العمولة تُحسب فقط عند إتمام الصفقة بنجاح</li>
                <li>لديك 30 يوم لدفع العمولة من تاريخ إتمام الصفقة</li>
                <li>يمكنك دفع عدة عمولات مرة واحدة</li>
            </ul>
        </div>
    </div>
</div>

<!-- إحصائيات العمولات -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['pending']; ?></h4>
                <p class="mb-1">في الانتظار</p>
                <small><?php echo formatPrice($amounts['pending_amount']); ?></small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['overdue']; ?></h4>
                <p class="mb-1">متأخرة</p>
                <small><?php echo formatPrice($amounts['overdue_amount']); ?></small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['paid']; ?></h4>
                <p class="mb-1">مدفوعة</p>
                <small><?php echo formatPrice($amounts['paid_amount']); ?></small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['total']; ?></h4>
                <p class="mb-1">إجمالي</p>
                <small><?php echo formatPrice($amounts['total_amount']); ?></small>
            </div>
        </div>
    </div>
</div>

<!-- تنبيه العمولات المتأخرة -->
<?php if ($stats['overdue'] > 0): ?>
<div class="alert alert-danger">
    <div class="d-flex align-items-center">
        <i class="fas fa-exclamation-triangle fs-4 me-3"></i>
        <div class="flex-grow-1">
            <h6 class="mb-1">تنبيه: عمولات متأخرة!</h6>
            <p class="mb-0">لديك <?php echo $stats['overdue']; ?> عمولة متأخرة بقيمة <?php echo formatPrice($amounts['overdue_amount']); ?>. يرجى الدفع في أقرب وقت لتجنب رسوم التأخير.</p>
        </div>
        <a href="commission-payment.php?status=overdue" class="btn btn-light">
            <i class="fas fa-credit-card me-1"></i>
            ادفع الآن
        </a>
    </div>
</div>
<?php endif; ?>

<!-- فلاتر -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">حالة الدفع</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                    <option value="overdue" <?php echo $status_filter === 'overdue' ? 'selected' : ''; ?>>متأخرة</option>
                    <option value="paid" <?php echo $status_filter === 'paid' ? 'selected' : ''; ?>>مدفوعة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>
                        فلترة
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة العمولات -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">عمولاتي (<?php echo $total_commissions; ?>)</h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($commissions)): ?>
            <div class="text-center py-5">
                <i class="fas fa-percentage fs-1 text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد عمولات</h5>
                <p class="text-muted">لم تحصل على أي صفقات تتطلب عمولة بعد</p>
                <a href="../request-part.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    اطلب قطعة غيار
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الصفقة</th>
                            <th>المحل</th>
                            <th>قيمة الصفقة</th>
                            <th>العمولة المستحقة</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($commissions as $commission): ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($commission['part_name']); ?></strong>
                                        <br><small class="text-muted">
                                            <?php echo htmlspecialchars($commission['car_make'] . ' ' . $commission['car_model'] . ' ' . $commission['car_year']); ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <?php echo htmlspecialchars($commission['business_name'] ?: $commission['vendor_name']); ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="fw-bold text-success">
                                        <?php echo formatPrice($commission['transaction_amount']); ?>
                                    </div>
                                    <small class="text-muted">سعر العرض: <?php echo formatPrice($commission['offer_price']); ?></small>
                                </td>
                                <td>
                                    <div class="fw-bold text-primary">
                                        <?php echo formatPrice($commission['customer_commission_amount']); ?>
                                    </div>
                                    <small class="text-muted"><?php echo $commission['customer_commission_rate']; ?>%</small>
                                </td>
                                <td>
                                    <div class="<?php echo strtotime($commission['due_date']) < time() ? 'text-danger' : 'text-success'; ?>">
                                        <?php echo formatArabicDate($commission['due_date']); ?>
                                    </div>
                                    <?php if (strtotime($commission['due_date']) < time() && $commission['customer_payment_status'] !== 'paid'): ?>
                                        <small class="text-danger">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            متأخر <?php echo floor((time() - strtotime($commission['due_date'])) / 86400); ?> يوم
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo $commission['customer_payment_status'] === 'pending' ? 'warning' : 
                                            ($commission['customer_payment_status'] === 'paid' ? 'success' : 
                                            ($commission['customer_payment_status'] === 'overdue' ? 'danger' : 'secondary')); 
                                    ?> fs-6">
                                        <?php 
                                        $status_text = [
                                            'pending' => 'في الانتظار',
                                            'paid' => 'مدفوعة',
                                            'overdue' => 'متأخرة',
                                            'waived' => 'معفاة'
                                        ];
                                        echo $status_text[$commission['customer_payment_status']] ?? $commission['customer_payment_status'];
                                        ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="commission-details.php?id=<?php echo $commission['id']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if ($commission['customer_payment_status'] !== 'paid'): ?>
                                            <a href="commission-payment.php?commission_id=<?php echo $commission['id']; ?>" class="btn btn-sm btn-success">
                                                <i class="fas fa-credit-card"></i>
                                            </a>
                                        <?php endif; ?>
                                        <a href="commission-invoice.php?id=<?php echo $commission['id']; ?>" class="btn btn-sm btn-outline-secondary" target="_blank">
                                            <i class="fas fa-file-pdf"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- ترقيم الصفحات -->
    <?php if ($total_pages > 1): ?>
        <div class="card-footer">
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>">السابق</a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>"><?php echo $i; ?></a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>">التالي</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>
