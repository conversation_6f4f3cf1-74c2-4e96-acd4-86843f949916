<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'customer') {
    showAlert('يجب تسجيل الدخول كعميل للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();
$customer_id = $_SESSION['user_id'];

// إحصائيات العميل
$stats = [];

// عدد الطلبات
$stmt = $db->query("SELECT COUNT(*) as total FROM part_requests WHERE customer_id = ?", [$customer_id]);
$stats['total_requests'] = $stmt->fetch()['total'];

// الطلبات النشطة
$stmt = $db->query("SELECT COUNT(*) as active FROM part_requests WHERE customer_id = ? AND status IN ('pending', 'active')", [$customer_id]);
$stats['active_requests'] = $stmt->fetch()['active'];

// العروض المستلمة
$stmt = $db->query("SELECT COUNT(*) as offers FROM offers o JOIN part_requests pr ON o.request_id = pr.id WHERE pr.customer_id = ?", [$customer_id]);
$stats['total_offers'] = $stmt->fetch()['offers'];

// العروض المقبولة
$stmt = $db->query("SELECT COUNT(*) as accepted FROM offers o JOIN part_requests pr ON o.request_id = pr.id WHERE pr.customer_id = ? AND o.status = 'accepted'", [$customer_id]);
$stats['accepted_offers'] = $stmt->fetch()['accepted'];

// أحدث الطلبات
$recent_requests = $db->query(
    "SELECT * FROM part_requests WHERE customer_id = ? ORDER BY created_at DESC LIMIT 5",
    [$customer_id]
)->fetchAll();

// أحدث العروض
$recent_offers = $db->query(
    "SELECT o.*, pr.part_name, pr.car_make, pr.car_model, pr.car_year, u.name as vendor_name, vp.business_name 
     FROM offers o 
     JOIN part_requests pr ON o.request_id = pr.id 
     JOIN users u ON o.vendor_id = u.id 
     LEFT JOIN vendor_profiles vp ON u.id = vp.user_id 
     WHERE pr.customer_id = ? 
     ORDER BY o.created_at DESC LIMIT 5",
    [$customer_id]
)->fetchAll();

$page_title = 'لوحة تحكم العميل';
include '../includes/header.php';
?>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-list-alt fs-1 mb-2"></i>
                <h3 class="mb-1"><?php echo $stats['total_requests']; ?></h3>
                <p class="mb-0">إجمالي الطلبات</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-clock fs-1 mb-2"></i>
                <h3 class="mb-1"><?php echo $stats['active_requests']; ?></h3>
                <p class="mb-0">طلبات نشطة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-handshake fs-1 mb-2"></i>
                <h3 class="mb-1"><?php echo $stats['total_offers']; ?></h3>
                <p class="mb-0">عروض مستلمة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fs-1 mb-2"></i>
                <h3 class="mb-1"><?php echo $stats['accepted_offers']; ?></h3>
                <p class="mb-0">عروض مقبولة</p>
            </div>
        </div>
    </div>
</div>

<!-- أزرار سريعة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title mb-3">إجراءات سريعة</h5>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="../request-part.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        طلب قطعة غيار جديدة
                    </a>
                    <a href="my-requests.php" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>
                        طلباتي
                    </a>
                    <a href="offers.php" class="btn btn-outline-success">
                        <i class="fas fa-handshake me-2"></i>
                        العروض المستلمة
                    </a>
                    <a href="commissions.php" class="btn btn-outline-warning">
                        <i class="fas fa-percentage me-2"></i>
                        عمولات الخدمة
                    </a>
                    <a href="../profile.php" class="btn btn-outline-secondary">
                        <i class="fas fa-user me-2"></i>
                        الملف الشخصي
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- أحدث الطلبات -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>
                    أحدث الطلبات
                </h5>
                <a href="my-requests.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <?php if (empty($recent_requests)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fs-1 mb-3"></i>
                        <p>لا توجد طلبات بعد</p>
                        <a href="../request-part.php" class="btn btn-primary">اطلب قطعة غيار الآن</a>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_requests as $request): ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1"><?php echo $request['part_name']; ?></h6>
                                        <p class="mb-1 text-muted small">
                                            <?php echo $request['car_make'] . ' ' . $request['car_model'] . ' ' . $request['car_year']; ?>
                                        </p>
                                        <small class="text-muted">
                                            <?php echo formatArabicDate($request['created_at']); ?>
                                        </small>
                                    </div>
                                    <span class="badge bg-<?php 
                                        echo $request['status'] === 'pending' ? 'warning' : 
                                            ($request['status'] === 'active' ? 'success' : 
                                            ($request['status'] === 'closed' ? 'secondary' : 'danger')); 
                                    ?>">
                                        <?php 
                                        $status_text = [
                                            'pending' => 'في الانتظار',
                                            'active' => 'نشط',
                                            'closed' => 'مغلق',
                                            'cancelled' => 'ملغي'
                                        ];
                                        echo $status_text[$request['status']] ?? $request['status'];
                                        ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- أحدث العروض -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-handshake me-2"></i>
                    أحدث العروض
                </h5>
                <a href="offers.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <?php if (empty($recent_offers)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-handshake fs-1 mb-3"></i>
                        <p>لا توجد عروض بعد</p>
                        <small>ستظهر العروض هنا عند استلامها من المحلات</small>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_offers as $offer): ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1"><?php echo $offer['part_name']; ?></h6>
                                        <p class="mb-1 text-muted small">
                                            من: <?php echo $offer['business_name'] ?: $offer['vendor_name']; ?>
                                        </p>
                                        <small class="text-muted">
                                            <?php echo formatArabicDate($offer['created_at']); ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-success">
                                            <?php echo formatPrice($offer['price']); ?>
                                        </div>
                                        <span class="badge bg-<?php 
                                            echo $offer['status'] === 'pending' ? 'warning' : 
                                                ($offer['status'] === 'accepted' ? 'success' : 
                                                ($offer['status'] === 'rejected' ? 'danger' : 'secondary')); 
                                        ?>">
                                            <?php 
                                            $status_text = [
                                                'pending' => 'في الانتظار',
                                                'accepted' => 'مقبول',
                                                'rejected' => 'مرفوض',
                                                'expired' => 'منتهي الصلاحية'
                                            ];
                                            echo $status_text[$offer['status']] ?? $offer['status'];
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
