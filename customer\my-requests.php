<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'customer') {
    showAlert('يجب تسجيل الدخول كعميل للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();
$customer_id = $_SESSION['user_id'];

// معالجة إلغاء الطلب
if ($_POST && isset($_POST['cancel_request'])) {
    $request_id = intval($_POST['request_id']);
    
    // التحقق من ملكية الطلب
    $stmt = $db->query("SELECT id FROM part_requests WHERE id = ? AND customer_id = ?", [$request_id, $customer_id]);
    if ($stmt->fetch()) {
        $db->query("UPDATE part_requests SET status = 'cancelled' WHERE id = ?", [$request_id]);
        showAlert('تم إلغاء الطلب بنجاح', 'success');
    } else {
        showAlert('لا يمكن إلغاء هذا الطلب', 'error');
    }
    redirect('my-requests.php');
}

// فلترة الطلبات
$status_filter = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

// بناء الاستعلام
$where_conditions = ["customer_id = ?"];
$params = [$customer_id];

if (!empty($status_filter)) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// عدد الطلبات الإجمالي
$count_query = "SELECT COUNT(*) as total FROM part_requests WHERE $where_clause";
$total_requests = $db->query($count_query, $params)->fetch()['total'];
$total_pages = ceil($total_requests / $per_page);

// جلب الطلبات مع عدد العروض
$requests_query = "SELECT pr.*, 
                   (SELECT COUNT(*) FROM offers WHERE request_id = pr.id) as offers_count,
                   (SELECT COUNT(*) FROM offers WHERE request_id = pr.id AND status = 'pending') as pending_offers_count
                   FROM part_requests pr 
                   WHERE $where_clause 
                   ORDER BY pr.created_at DESC 
                   LIMIT $per_page OFFSET $offset";

$requests = $db->query($requests_query, $params)->fetchAll();

$page_title = 'طلباتي';
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold text-primary">
        <i class="fas fa-list-alt me-2"></i>
        طلباتي
    </h2>
    <div class="d-flex gap-2">
        <a href="../request-part.php" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            طلب جديد
        </a>
        <a href="dashboard.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            لوحة التحكم
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <?php
    $stats = [];
    $stats['total'] = $db->query("SELECT COUNT(*) as count FROM part_requests WHERE customer_id = ?", [$customer_id])->fetch()['count'];
    $stats['pending'] = $db->query("SELECT COUNT(*) as count FROM part_requests WHERE customer_id = ? AND status = 'pending'", [$customer_id])->fetch()['count'];
    $stats['active'] = $db->query("SELECT COUNT(*) as count FROM part_requests WHERE customer_id = ? AND status = 'active'", [$customer_id])->fetch()['count'];
    $stats['closed'] = $db->query("SELECT COUNT(*) as count FROM part_requests WHERE customer_id = ? AND status = 'closed'", [$customer_id])->fetch()['count'];
    ?>
    
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['total']; ?></h4>
                <p class="mb-0">إجمالي الطلبات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['pending']; ?></h4>
                <p class="mb-0">في الانتظار</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['active']; ?></h4>
                <p class="mb-0">نشطة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['closed']; ?></h4>
                <p class="mb-0">مغلقة</p>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">حالة الطلب</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                    <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>نشط</option>
                    <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>مغلق</option>
                    <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>
                        فلترة
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الطلبات -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">طلباتي (<?php echo $total_requests; ?>)</h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($requests)): ?>
            <div class="text-center py-5">
                <i class="fas fa-inbox fs-1 text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد طلبات</h5>
                <p class="text-muted">لم تقم بإنشاء أي طلبات بعد</p>
                <a href="../request-part.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    اطلب قطعة غيار الآن
                </a>
            </div>
        <?php else: ?>
            <div class="list-group list-group-flush">
                <?php foreach ($requests as $request): ?>
                    <div class="list-group-item">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="mb-1 fw-bold"><?php echo htmlspecialchars($request['part_name']); ?></h6>
                                <p class="mb-1 text-muted">
                                    <i class="fas fa-car me-1"></i>
                                    <?php echo htmlspecialchars($request['car_make'] . ' ' . $request['car_model'] . ' ' . $request['car_year']); ?>
                                </p>
                                <?php if ($request['part_number']): ?>
                                    <p class="mb-1 text-muted small">
                                        <i class="fas fa-barcode me-1"></i>
                                        رقم القطعة: <?php echo htmlspecialchars($request['part_number']); ?>
                                    </p>
                                <?php endif; ?>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    <?php echo formatArabicDate($request['created_at']); ?>
                                </small>
                            </div>
                            
                            <div class="col-md-2 text-center">
                                <span class="badge bg-<?php 
                                    echo $request['status'] === 'pending' ? 'warning' : 
                                        ($request['status'] === 'active' ? 'success' : 
                                        ($request['status'] === 'closed' ? 'secondary' : 'danger')); 
                                ?> fs-6">
                                    <?php 
                                    $status_text = [
                                        'pending' => 'في الانتظار',
                                        'active' => 'نشط',
                                        'closed' => 'مغلق',
                                        'cancelled' => 'ملغي'
                                    ];
                                    echo $status_text[$request['status']] ?? $request['status'];
                                    ?>
                                </span>
                            </div>
                            
                            <div class="col-md-2 text-center">
                                <div class="fw-bold text-primary"><?php echo $request['offers_count']; ?></div>
                                <small class="text-muted">عروض مستلمة</small>
                                <?php if ($request['pending_offers_count'] > 0): ?>
                                    <br><span class="badge bg-info"><?php echo $request['pending_offers_count']; ?> جديد</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="col-md-2 text-end">
                                <div class="btn-group">
                                    <a href="request-details.php?id=<?php echo $request['id']; ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                        عرض
                                    </a>
                                    <?php if ($request['offers_count'] > 0): ?>
                                        <a href="offers.php?request_id=<?php echo $request['id']; ?>" class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-handshake"></i>
                                            العروض
                                        </a>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if (in_array($request['status'], ['pending', 'active'])): ?>
                                    <form method="POST" class="d-inline mt-2">
                                        <input type="hidden" name="request_id" value="<?php echo $request['id']; ?>">
                                        <button type="submit" name="cancel_request" class="btn btn-sm btn-outline-danger" 
                                                onclick="return confirm('هل أنت متأكد من إلغاء هذا الطلب؟')">
                                            <i class="fas fa-times"></i>
                                            إلغاء
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <?php if ($request['description']): ?>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <strong>الوصف:</strong> <?php echo htmlspecialchars($request['description']); ?>
                                </small>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($request['budget_min'] || $request['budget_max']): ?>
                            <div class="mt-1">
                                <small class="text-muted">
                                    <i class="fas fa-money-bill-wave me-1"></i>
                                    الميزانية: 
                                    <?php if ($request['budget_min'] && $request['budget_max']): ?>
                                        <?php echo formatPrice($request['budget_min']) . ' - ' . formatPrice($request['budget_max']); ?>
                                    <?php elseif ($request['budget_max']): ?>
                                        حتى <?php echo formatPrice($request['budget_max']); ?>
                                    <?php elseif ($request['budget_min']): ?>
                                        من <?php echo formatPrice($request['budget_min']); ?>
                                    <?php endif; ?>
                                </small>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- ترقيم الصفحات -->
    <?php if ($total_pages > 1): ?>
        <div class="card-footer">
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>">السابق</a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>"><?php echo $i; ?></a>
                        </li>
                    <?php endforeach; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>">التالي</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>
