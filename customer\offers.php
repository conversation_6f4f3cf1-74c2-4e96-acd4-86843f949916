<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'customer') {
    showAlert('يجب تسجيل الدخول كعميل للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();
$customer_id = $_SESSION['user_id'];

// معالجة قبول/رفض العروض
if ($_POST) {
    if (isset($_POST['accept_offer'])) {
        $offer_id = intval($_POST['offer_id']);
        
        // التحقق من ملكية العرض
        $stmt = $db->query(
            "SELECT o.*, pr.customer_id FROM offers o 
             JOIN part_requests pr ON o.request_id = pr.id 
             WHERE o.id = ? AND pr.customer_id = ?",
            [$offer_id, $customer_id]
        );
        
        if ($offer = $stmt->fetch()) {
            // قبول العرض
            $db->query("UPDATE offers SET status = 'accepted' WHERE id = ?", [$offer_id]);
            
            // رفض باقي العروض للطلب نفسه
            $db->query(
                "UPDATE offers SET status = 'rejected' WHERE request_id = ? AND id != ?",
                [$offer['request_id'], $offer_id]
            );
            
            // إغلاق الطلب
            $db->query("UPDATE part_requests SET status = 'closed' WHERE id = ?", [$offer['request_id']]);
            
            showAlert('تم قبول العرض بنجاح! سيتم التواصل معك قريباً.', 'success');
        } else {
            showAlert('لا يمكن قبول هذا العرض', 'error');
        }
    } elseif (isset($_POST['reject_offer'])) {
        $offer_id = intval($_POST['offer_id']);
        
        // التحقق من ملكية العرض
        $stmt = $db->query(
            "SELECT o.*, pr.customer_id FROM offers o 
             JOIN part_requests pr ON o.request_id = pr.id 
             WHERE o.id = ? AND pr.customer_id = ?",
            [$offer_id, $customer_id]
        );
        
        if ($stmt->fetch()) {
            $db->query("UPDATE offers SET status = 'rejected' WHERE id = ?", [$offer_id]);
            showAlert('تم رفض العرض', 'info');
        } else {
            showAlert('لا يمكن رفض هذا العرض', 'error');
        }
    }
    
    redirect('offers.php');
}

// فلترة العروض
$request_id = $_GET['request_id'] ?? '';
$status_filter = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

// بناء الاستعلام
$where_conditions = ["pr.customer_id = ?"];
$params = [$customer_id];

if (!empty($request_id)) {
    $where_conditions[] = "o.request_id = ?";
    $params[] = $request_id;
}

if (!empty($status_filter)) {
    $where_conditions[] = "o.status = ?";
    $params[] = $status_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// عدد العروض الإجمالي
$count_query = "SELECT COUNT(*) as total FROM offers o 
                JOIN part_requests pr ON o.request_id = pr.id 
                WHERE $where_clause";
$total_offers = $db->query($count_query, $params)->fetch()['total'];
$total_pages = ceil($total_offers / $per_page);

// جلب العروض
$offers_query = "SELECT o.*, pr.part_name, pr.car_make, pr.car_model, pr.car_year,
                 u.name as vendor_name, vp.business_name, vp.city, vp.rating
                 FROM offers o 
                 JOIN part_requests pr ON o.request_id = pr.id 
                 JOIN users u ON o.vendor_id = u.id 
                 LEFT JOIN vendor_profiles vp ON u.id = vp.user_id 
                 WHERE $where_clause 
                 ORDER BY o.created_at DESC 
                 LIMIT $per_page OFFSET $offset";

$offers = $db->query($offers_query, $params)->fetchAll();

// جلب طلبات العميل للفلترة
$customer_requests = $db->query(
    "SELECT id, part_name, car_make, car_model, car_year FROM part_requests 
     WHERE customer_id = ? ORDER BY created_at DESC",
    [$customer_id]
)->fetchAll();

$page_title = 'العروض المستلمة';
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold text-primary">
        <i class="fas fa-handshake me-2"></i>
        العروض المستلمة
    </h2>
    <div class="d-flex gap-2">
        <a href="my-requests.php" class="btn btn-outline-primary">
            <i class="fas fa-list me-1"></i>
            طلباتي
        </a>
        <a href="dashboard.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            لوحة التحكم
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <?php
    $stats = [];
    $stats['total'] = $db->query("SELECT COUNT(*) as count FROM offers o JOIN part_requests pr ON o.request_id = pr.id WHERE pr.customer_id = ?", [$customer_id])->fetch()['count'];
    $stats['pending'] = $db->query("SELECT COUNT(*) as count FROM offers o JOIN part_requests pr ON o.request_id = pr.id WHERE pr.customer_id = ? AND o.status = 'pending'", [$customer_id])->fetch()['count'];
    $stats['accepted'] = $db->query("SELECT COUNT(*) as count FROM offers o JOIN part_requests pr ON o.request_id = pr.id WHERE pr.customer_id = ? AND o.status = 'accepted'", [$customer_id])->fetch()['count'];
    $stats['rejected'] = $db->query("SELECT COUNT(*) as count FROM offers o JOIN part_requests pr ON o.request_id = pr.id WHERE pr.customer_id = ? AND o.status = 'rejected'", [$customer_id])->fetch()['count'];
    ?>
    
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['total']; ?></h4>
                <p class="mb-0">إجمالي العروض</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['pending']; ?></h4>
                <p class="mb-0">في الانتظار</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['accepted']; ?></h4>
                <p class="mb-0">مقبولة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['rejected']; ?></h4>
                <p class="mb-0">مرفوضة</p>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">الطلب</label>
                <select name="request_id" class="form-select">
                    <option value="">جميع الطلبات</option>
                    <?php foreach ($customer_requests as $req): ?>
                        <option value="<?php echo $req['id']; ?>" <?php echo $request_id == $req['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($req['part_name'] . ' - ' . $req['car_make'] . ' ' . $req['car_model']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">حالة العرض</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                    <option value="accepted" <?php echo $status_filter === 'accepted' ? 'selected' : ''; ?>>مقبول</option>
                    <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>مرفوض</option>
                    <option value="expired" <?php echo $status_filter === 'expired' ? 'selected' : ''; ?>>منتهي الصلاحية</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>
                        فلترة
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة العروض -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">العروض المستلمة (<?php echo $total_offers; ?>)</h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($offers)): ?>
            <div class="text-center py-5">
                <i class="fas fa-handshake fs-1 text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد عروض</h5>
                <p class="text-muted">لم تستلم أي عروض بعد</p>
                <a href="../request-part.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    اطلب قطعة غيار جديدة
                </a>
            </div>
        <?php else: ?>
            <div class="list-group list-group-flush">
                <?php foreach ($offers as $offer): ?>
                    <div class="list-group-item">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <h6 class="mb-1 fw-bold"><?php echo htmlspecialchars($offer['part_name']); ?></h6>
                                <p class="mb-1 text-muted small">
                                    <i class="fas fa-car me-1"></i>
                                    <?php echo htmlspecialchars($offer['car_make'] . ' ' . $offer['car_model'] . ' ' . $offer['car_year']); ?>
                                </p>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    <?php echo formatArabicDate($offer['created_at']); ?>
                                </small>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="fw-bold"><?php echo htmlspecialchars($offer['business_name'] ?: $offer['vendor_name']); ?></div>
                                <?php if ($offer['city']): ?>
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <?php echo htmlspecialchars($offer['city']); ?>
                                    </small>
                                <?php endif; ?>
                                <?php if ($offer['rating'] > 0): ?>
                                    <div class="text-warning small">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star<?php echo $i <= $offer['rating'] ? '' : '-o'; ?>"></i>
                                        <?php endfor; ?>
                                        <span class="text-muted">(<?php echo number_format($offer['rating'], 1); ?>)</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="col-md-2 text-center">
                                <div class="fw-bold text-success fs-5"><?php echo formatPrice($offer['price']); ?></div>
                                <?php if ($offer['delivery_cost'] > 0): ?>
                                    <small class="text-muted">+ <?php echo formatPrice($offer['delivery_cost']); ?> توصيل</small>
                                <?php endif; ?>
                                <div class="small text-muted">
                                    <?php echo $offer['condition_type'] === 'new' ? 'جديد' : 'مستعمل'; ?>
                                    <?php if ($offer['warranty_period'] > 0): ?>
                                        <br>ضمان <?php echo $offer['warranty_period']; ?> شهر
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="col-md-2 text-center">
                                <span class="badge bg-<?php 
                                    echo $offer['status'] === 'pending' ? 'warning' : 
                                        ($offer['status'] === 'accepted' ? 'success' : 
                                        ($offer['status'] === 'rejected' ? 'danger' : 'secondary')); 
                                ?> fs-6">
                                    <?php 
                                    $status_text = [
                                        'pending' => 'في الانتظار',
                                        'accepted' => 'مقبول',
                                        'rejected' => 'مرفوض',
                                        'expired' => 'منتهي الصلاحية'
                                    ];
                                    echo $status_text[$offer['status']] ?? $offer['status'];
                                    ?>
                                </span>
                            </div>
                            
                            <div class="col-md-1 text-end">
                                <?php if ($offer['status'] === 'pending'): ?>
                                    <div class="btn-group-vertical">
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="offer_id" value="<?php echo $offer['id']; ?>">
                                            <button type="submit" name="accept_offer" class="btn btn-sm btn-success mb-1" 
                                                    onclick="return confirm('هل أنت متأكد من قبول هذا العرض؟ سيتم رفض باقي العروض تلقائياً.')">
                                                <i class="fas fa-check"></i>
                                                قبول
                                            </button>
                                        </form>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="offer_id" value="<?php echo $offer['id']; ?>">
                                            <button type="submit" name="reject_offer" class="btn btn-sm btn-outline-danger" 
                                                    onclick="return confirm('هل أنت متأكد من رفض هذا العرض؟')">
                                                <i class="fas fa-times"></i>
                                                رفض
                                            </button>
                                        </form>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <?php if ($offer['notes']): ?>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <strong>ملاحظات المحل:</strong> <?php echo htmlspecialchars($offer['notes']); ?>
                                </small>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($offer['delivery_time']): ?>
                            <div class="mt-1">
                                <small class="text-muted">
                                    <i class="fas fa-truck me-1"></i>
                                    وقت التوصيل: <?php echo htmlspecialchars($offer['delivery_time']); ?>
                                </small>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- ترقيم الصفحات -->
    <?php if ($total_pages > 1): ?>
        <div class="card-footer">
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&request_id=<?php echo $request_id; ?>&status=<?php echo $status_filter; ?>">السابق</a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>&request_id=<?php echo $request_id; ?>&status=<?php echo $status_filter; ?>"><?php echo $i; ?></a>
                        </li>
                    <?php endforeach; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&request_id=<?php echo $request_id; ?>&status=<?php echo $status_filter; ?>">التالي</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>
