<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'customer') {
    showAlert('يجب تسجيل الدخول كعميل للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();
$customer_id = $_SESSION['user_id'];
$offer_id = intval($_GET['id'] ?? 0);

if (!$offer_id) {
    showAlert('عرض غير صحيح', 'error');
    redirect('offers.php');
}

// جلب تفاصيل العرض
$offer = $db->query(
    "SELECT o.*, pr.part_name, pr.customer_id, v.name as vendor_name
     FROM offers o 
     JOIN part_requests pr ON o.request_id = pr.id 
     JOIN users v ON o.vendor_id = v.id
     WHERE o.id = ? AND pr.customer_id = ? AND o.status = 'pending'",
    [$offer_id, $customer_id]
)->fetch();

if (!$offer) {
    showAlert('العرض غير موجود أو تم قبوله/رفضه مسبقاً', 'error');
    redirect('offers.php');
}

// معالجة رفض العرض
if ($_POST && isset($_POST['confirm_reject'])) {
    $rejection_reason = trim($_POST['rejection_reason'] ?? '');
    
    try {
        // تحديث حالة العرض إلى مرفوض
        $db->query(
            "UPDATE offers SET status = 'rejected', rejected_at = NOW(), rejection_reason = ? WHERE id = ?",
            [$rejection_reason, $offer_id]
        );
        
        // إرسال رسالة في المحادثة إذا كانت موجودة
        $conversation_id = createOrGetConversation($offer_id, $customer_id, $offer['vendor_id']);
        $reject_message = "شكراً لعرضك، لكن لن أتمكن من قبوله.";
        if (!empty($rejection_reason)) {
            $reject_message .= " السبب: " . $rejection_reason;
        }
        sendMessage($conversation_id, $customer_id, 'customer', $reject_message, 'system');
        
        // إنشاء تنبيه للمحل
        createNotification(
            $offer['vendor_id'],
            'offer_rejected',
            'تم رفض عرضك',
            "العميل {$_SESSION['user_name']} رفض عرضك لقطعة {$offer['part_name']}" . 
            (!empty($rejection_reason) ? " - السبب: {$rejection_reason}" : ""),
            [
                'offer_id' => $offer_id,
                'customer_name' => $_SESSION['user_name'],
                'part_name' => $offer['part_name'],
                'rejection_reason' => $rejection_reason
            ],
            "vendor/offer-details.php?id={$offer_id}"
        );
        
        showAlert('تم رفض العرض وإشعار المحل.', 'success');
        redirect("offers.php?request_id={$offer['request_id']}");
        
    } catch (Exception $e) {
        showAlert('حدث خطأ أثناء رفض العرض: ' . $e->getMessage(), 'error');
    }
}

$page_title = 'رفض العرض';
include '../includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-danger text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-times-circle me-2"></i>
                    رفض العرض
                </h4>
            </div>
            <div class="card-body p-4">
                
                <!-- تفاصيل العرض -->
                <div class="alert alert-info">
                    <h5 class="mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        تفاصيل العرض المراد رفضه
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>القطعة:</strong> <?php echo htmlspecialchars($offer['part_name']); ?></p>
                            <p><strong>المحل:</strong> <?php echo htmlspecialchars($offer['vendor_name']); ?></p>
                            <p><strong>السعر:</strong> <span class="text-success fw-bold fs-4"><?php echo formatPrice($offer['price']); ?></span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>حالة القطعة:</strong> 
                                <span class="badge bg-<?php echo $offer['condition_type'] === 'new' ? 'success' : 'warning'; ?>">
                                    <?php echo $offer['condition_type'] === 'new' ? 'جديدة' : 'مستعملة'; ?>
                                </span>
                            </p>
                            <p><strong>فترة الضمان:</strong> <?php echo $offer['warranty_period'] > 0 ? $offer['warranty_period'] . ' شهر' : 'بدون ضمان'; ?></p>
                            <p><strong>تكلفة التوصيل:</strong> <?php echo $offer['delivery_cost'] > 0 ? formatPrice($offer['delivery_cost']) : 'مجاني'; ?></p>
                        </div>
                    </div>
                </div>
                
                <!-- نموذج الرفض -->
                <form method="POST">
                    <div class="mb-4">
                        <label class="form-label fw-bold">سبب الرفض (اختياري):</label>
                        <select name="rejection_reason" class="form-select mb-3">
                            <option value="">اختر سبب الرفض</option>
                            <option value="السعر مرتفع">السعر مرتفع</option>
                            <option value="وجدت عرض أفضل">وجدت عرض أفضل</option>
                            <option value="لا أحتاج القطعة الآن">لا أحتاج القطعة الآن</option>
                            <option value="شكوك في جودة القطعة">شكوك في جودة القطعة</option>
                            <option value="مدة التوصيل طويلة">مدة التوصيل طويلة</option>
                            <option value="تكلفة التوصيل مرتفعة">تكلفة التوصيل مرتفعة</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                        
                        <textarea name="rejection_reason_custom" class="form-control" rows="3" 
                                  placeholder="أو اكتب سبب الرفض هنا... (اختياري)"></textarea>
                        <div class="form-text">
                            تقديم سبب الرفض يساعد المحل على تحسين عروضه المستقبلية
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه:</h6>
                        <ul class="mb-0">
                            <li>برفضك لهذا العرض، لن تتمكن من قبوله مرة أخرى</li>
                            <li>سيتم إشعار المحل برفض العرض</li>
                            <li>يمكنك الاستمرار في تلقي عروض أخرى لنفس الطلب</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex gap-3 justify-content-center">
                        <button type="submit" name="confirm_reject" class="btn btn-danger btn-lg px-5">
                            <i class="fas fa-times me-2"></i>
                            تأكيد رفض العرض
                        </button>
                        
                        <a href="offers.php?request_id=<?php echo $offer['request_id']; ?>" class="btn btn-outline-secondary btn-lg px-5">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للعروض
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- نصائح -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح قبل رفض العرض
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li class="mb-2">تأكد من مراجعة جميع تفاصيل العرض بعناية</li>
                    <li class="mb-2">يمكنك التواصل مع المحل لمناقشة تعديل العرض</li>
                    <li class="mb-2">قارن هذا العرض مع العروض الأخرى المتاحة</li>
                    <li class="mb-2">تذكر أن تقديم سبب الرفض يساعد في تحسين الخدمة</li>
                    <li class="mb-0">يمكنك دائماً إنشاء طلب جديد إذا تغيرت احتياجاتك</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// دمج سبب الرفض المخصص مع المحدد
document.querySelector('form').addEventListener('submit', function(e) {
    const selectReason = document.querySelector('select[name="rejection_reason"]').value;
    const customReason = document.querySelector('textarea[name="rejection_reason_custom"]').value.trim();
    
    let finalReason = '';
    if (selectReason && selectReason !== 'أخرى') {
        finalReason = selectReason;
    } else if (customReason) {
        finalReason = customReason;
    }
    
    // تحديث القيمة النهائية
    document.querySelector('select[name="rejection_reason"]').value = finalReason;
    
    // تأكيد الرفض
    if (!confirm('هل أنت متأكد من رفض هذا العرض؟')) {
        e.preventDefault();
    }
});
</script>

<?php include '../includes/footer.php'; ?>
