<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'customer') {
    showAlert('يجب تسجيل الدخول كعميل للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();
$customer_id = $_SESSION['user_id'];
$request_id = intval($_GET['id'] ?? 0);

if (!$request_id) {
    showAlert('طلب غير صحيح', 'error');
    redirect('my-requests.php');
}

// جلب تفاصيل الطلب
$request = $db->query(
    "SELECT * FROM part_requests WHERE id = ? AND customer_id = ?",
    [$request_id, $customer_id]
)->fetch();

if (!$request) {
    showAlert('الطلب غير موجود', 'error');
    redirect('my-requests.php');
}

// جلب العروض على هذا الطلب
$offers = $db->query(
    "SELECT o.*, u.name as vendor_name, vp.business_name, vp.city, vp.rating, vp.address
     FROM offers o 
     JOIN users u ON o.vendor_id = u.id 
     LEFT JOIN vendor_profiles vp ON u.id = vp.user_id 
     WHERE o.request_id = ? 
     ORDER BY o.price ASC",
    [$request_id]
)->fetchAll();

$page_title = 'تفاصيل الطلب';
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold text-primary">
        <i class="fas fa-eye me-2"></i>
        تفاصيل الطلب
    </h2>
    <div class="d-flex gap-2">
        <a href="my-requests.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة لطلباتي
        </a>
    </div>
</div>

<div class="row">
    <!-- تفاصيل الطلب -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل الطلب
                </h5>
                <span class="badge bg-<?php 
                    echo $request['status'] === 'pending' ? 'warning' : 
                        ($request['status'] === 'active' ? 'success' : 
                        ($request['status'] === 'closed' ? 'secondary' : 'danger')); 
                ?> fs-6">
                    <?php 
                    $status_text = [
                        'pending' => 'في الانتظار',
                        'active' => 'نشط',
                        'closed' => 'مغلق',
                        'cancelled' => 'ملغي'
                    ];
                    echo $status_text[$request['status']] ?? $request['status'];
                    ?>
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">اسم القطعة:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($request['part_name']); ?></p>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">رقم القطعة:</label>
                        <p class="mb-0"><?php echo $request['part_number'] ? htmlspecialchars($request['part_number']) : 'غير محدد'; ?></p>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">ماركة السيارة:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($request['car_make']); ?></p>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">الموديل:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($request['car_model']); ?></p>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">السنة:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($request['car_year']); ?></p>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">حالة القطعة المطلوبة:</label>
                        <p class="mb-0">
                            <span class="badge bg-<?php 
                                echo $request['condition_type'] === 'new' ? 'success' : 
                                    ($request['condition_type'] === 'used' ? 'warning' : 'info'); 
                            ?>">
                                <?php 
                                $condition_text = [
                                    'new' => 'جديدة فقط',
                                    'used' => 'مستعملة فقط',
                                    'both' => 'جديدة أو مستعملة'
                                ];
                                echo $condition_text[$request['condition_type']] ?? $request['condition_type'];
                                ?>
                            </span>
                        </p>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">الأولوية:</label>
                        <p class="mb-0">
                            <span class="badge bg-<?php 
                                echo $request['urgency'] === 'high' ? 'danger' : 
                                    ($request['urgency'] === 'medium' ? 'warning' : 'secondary'); 
                            ?>">
                                <?php 
                                $urgency_text = [
                                    'high' => 'عاجل',
                                    'medium' => 'متوسط',
                                    'low' => 'عادي'
                                ];
                                echo $urgency_text[$request['urgency']] ?? $request['urgency'];
                                ?>
                            </span>
                        </p>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">الميزانية:</label>
                        <p class="mb-0">
                            <?php if ($request['budget_min'] || $request['budget_max']): ?>
                                <?php if ($request['budget_min'] && $request['budget_max']): ?>
                                    <?php echo formatPrice($request['budget_min']) . ' - ' . formatPrice($request['budget_max']); ?>
                                <?php elseif ($request['budget_max']): ?>
                                    حتى <?php echo formatPrice($request['budget_max']); ?>
                                <?php elseif ($request['budget_min']): ?>
                                    من <?php echo formatPrice($request['budget_min']); ?>
                                <?php endif; ?>
                            <?php else: ?>
                                غير محددة
                            <?php endif; ?>
                        </p>
                    </div>
                    
                    <?php if ($request['location']): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">الموقع:</label>
                            <p class="mb-0">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?php echo htmlspecialchars($request['location']); ?>
                            </p>
                        </div>
                    <?php endif; ?>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تاريخ الطلب:</label>
                        <p class="mb-0"><?php echo formatArabicDate($request['created_at']); ?></p>
                    </div>
                    
                    <?php if ($request['expires_at']): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">تاريخ انتهاء الصلاحية:</label>
                            <p class="mb-0 <?php echo strtotime($request['expires_at']) < time() ? 'text-danger' : 'text-success'; ?>">
                                <?php echo formatArabicDate($request['expires_at']); ?>
                                <?php if (strtotime($request['expires_at']) < time()): ?>
                                    <span class="badge bg-danger ms-1">منتهي الصلاحية</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($request['description']): ?>
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold">الوصف:</label>
                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($request['description'])); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- صور الطلب -->
                <?php if ($request['images']): ?>
                    <div class="mt-3">
                        <label class="form-label fw-bold">صور القطعة:</label>
                        <div class="row">
                            <?php 
                            $images = json_decode($request['images'], true);
                            if ($images):
                                foreach ($images as $image): 
                            ?>
                                <div class="col-md-3 mb-2">
                                    <img src="../uploads/requests/<?php echo htmlspecialchars($image); ?>" 
                                         class="img-thumbnail" 
                                         style="width: 100%; height: 150px; object-fit: cover; cursor: pointer;"
                                         data-bs-toggle="modal" 
                                         data-bs-target="#imageModal"
                                         onclick="showImage('../uploads/requests/<?php echo htmlspecialchars($image); ?>')">
                                </div>
                            <?php 
                                endforeach;
                            endif; 
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- العروض المستلمة -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-handshake me-2"></i>
                    العروض المستلمة (<?php echo count($offers); ?>)
                </h5>
                <?php if (!empty($offers)): ?>
                    <a href="offers.php?request_id=<?php echo $request_id; ?>" class="btn btn-sm btn-outline-primary">
                        عرض جميع العروض
                    </a>
                <?php endif; ?>
            </div>
            <div class="card-body p-0">
                <?php if (empty($offers)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-handshake fs-1 text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد عروض بعد</h5>
                        <p class="text-muted">لم تستلم أي عروض على هذا الطلب حتى الآن</p>
                        <?php if ($request['status'] === 'pending'): ?>
                            <small class="text-muted">ستصلك إشعارات عند وصول عروض جديدة</small>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($offers as $offer): ?>
                            <div class="list-group-item">
                                <div class="row align-items-center">
                                    <div class="col-md-4">
                                        <div class="fw-bold"><?php echo htmlspecialchars($offer['business_name'] ?: $offer['vendor_name']); ?></div>
                                        <?php if ($offer['city']): ?>
                                            <small class="text-muted">
                                                <i class="fas fa-map-marker-alt me-1"></i>
                                                <?php echo htmlspecialchars($offer['city']); ?>
                                            </small>
                                        <?php endif; ?>
                                        <?php if ($offer['rating'] > 0): ?>
                                            <div class="text-warning small">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star<?php echo $i <= $offer['rating'] ? '' : '-o'; ?>"></i>
                                                <?php endfor; ?>
                                                <span class="text-muted">(<?php echo number_format($offer['rating'], 1); ?>)</span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="col-md-2 text-center">
                                        <div class="fw-bold text-success fs-5"><?php echo formatPrice($offer['price']); ?></div>
                                        <?php if ($offer['delivery_cost'] > 0): ?>
                                            <small class="text-muted">+ <?php echo formatPrice($offer['delivery_cost']); ?> توصيل</small>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="col-md-2 text-center">
                                        <span class="badge bg-<?php echo $offer['condition_type'] === 'new' ? 'success' : 'warning'; ?>">
                                            <?php echo $offer['condition_type'] === 'new' ? 'جديد' : 'مستعمل'; ?>
                                        </span>
                                        <?php if ($offer['warranty_period'] > 0): ?>
                                            <br><small class="text-muted">ضمان <?php echo $offer['warranty_period']; ?> شهر</small>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="col-md-2 text-center">
                                        <span class="badge bg-<?php 
                                            echo $offer['status'] === 'pending' ? 'warning' : 
                                                ($offer['status'] === 'accepted' ? 'success' : 
                                                ($offer['status'] === 'rejected' ? 'danger' : 'secondary')); 
                                        ?> fs-6">
                                            <?php 
                                            $status_text = [
                                                'pending' => 'في الانتظار',
                                                'accepted' => 'مقبول',
                                                'rejected' => 'مرفوض',
                                                'expired' => 'منتهي الصلاحية'
                                            ];
                                            echo $status_text[$offer['status']] ?? $offer['status'];
                                            ?>
                                        </span>
                                    </div>
                                    
                                    <div class="col-md-2 text-end">
                                        <?php if ($offer['status'] === 'pending' && $request['status'] !== 'closed'): ?>
                                            <div class="btn-group-vertical">
                                                <form method="POST" action="offers.php" class="d-inline">
                                                    <input type="hidden" name="offer_id" value="<?php echo $offer['id']; ?>">
                                                    <button type="submit" name="accept_offer" class="btn btn-sm btn-success mb-1" 
                                                            onclick="return confirm('هل أنت متأكد من قبول هذا العرض؟ سيتم رفض باقي العروض تلقائياً.')">
                                                        <i class="fas fa-check"></i>
                                                        قبول
                                                    </button>
                                                </form>
                                                <form method="POST" action="offers.php" class="d-inline">
                                                    <input type="hidden" name="offer_id" value="<?php echo $offer['id']; ?>">
                                                    <button type="submit" name="reject_offer" class="btn btn-sm btn-outline-danger" 
                                                            onclick="return confirm('هل أنت متأكد من رفض هذا العرض؟')">
                                                        <i class="fas fa-times"></i>
                                                        رفض
                                                    </button>
                                                </form>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <?php if ($offer['notes']): ?>
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <strong>ملاحظات المحل:</strong> <?php echo htmlspecialchars($offer['notes']); ?>
                                        </small>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($offer['delivery_time']): ?>
                                    <div class="mt-1">
                                        <small class="text-muted">
                                            <i class="fas fa-truck me-1"></i>
                                            وقت التوصيل: <?php echo htmlspecialchars($offer['delivery_time']); ?>
                                        </small>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- معلومات إضافية -->
    <div class="col-lg-4">
        <!-- إحصائيات الطلب -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات الطلب
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary mb-1"><?php echo count($offers); ?></h4>
                            <small class="text-muted">عروض مستلمة</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <?php 
                        $pending_offers = array_filter($offers, function($offer) {
                            return $offer['status'] === 'pending';
                        });
                        ?>
                        <h4 class="text-warning mb-1"><?php echo count($pending_offers); ?></h4>
                        <small class="text-muted">في الانتظار</small>
                    </div>
                </div>
                
                <?php if (!empty($offers)): ?>
                    <hr>
                    <div class="text-center">
                        <small class="text-muted">أقل سعر:</small>
                        <div class="fw-bold text-success"><?php echo formatPrice(min(array_column($offers, 'price'))); ?></div>
                        <small class="text-muted">أعلى سعر:</small>
                        <div class="fw-bold text-danger"><?php echo formatPrice(max(array_column($offers, 'price'))); ?></div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- إجراءات سريعة -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if (!empty($offers) && $request['status'] !== 'closed'): ?>
                        <a href="offers.php?request_id=<?php echo $request_id; ?>" class="btn btn-primary">
                            <i class="fas fa-handshake me-2"></i>
                            مراجعة جميع العروض
                        </a>
                    <?php endif; ?>
                    
                    <?php if (in_array($request['status'], ['pending', 'active'])): ?>
                        <form method="POST" action="my-requests.php" class="d-inline">
                            <input type="hidden" name="request_id" value="<?php echo $request_id; ?>">
                            <button type="submit" name="cancel_request" class="btn btn-outline-danger w-100" 
                                    onclick="return confirm('هل أنت متأكد من إلغاء هذا الطلب؟')">
                                <i class="fas fa-times me-2"></i>
                                إلغاء الطلب
                            </button>
                        </form>
                    <?php endif; ?>
                    
                    <a href="../request-part.php" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>
                        طلب قطعة غيار جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض الصور -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">صورة القطعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<script>
function showImage(src) {
    document.getElementById('modalImage').src = src;
}
</script>

<?php include '../includes/footer.php'; ?>
