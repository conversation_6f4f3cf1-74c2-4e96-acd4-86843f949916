-- إنشاء قاعدة بيانات موقع صناعية
-- Create database for Sanaeya website

CREATE DATABASE IF NOT EXISTS sanaeya_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE sanaeya_db;

-- جدول المستخدمين (العملاء والمحلات)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20) NOT NULL,
    password VARCHAR(255) NOT NULL,
    user_type ENUM('customer', 'vendor', 'admin') NOT NULL DEFAULT 'customer',
    status ENUM('active', 'inactive', 'suspended') NOT NULL DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    reset_token VARCHAR(255),
    reset_token_expires DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_user_type (user_type),
    INDEX idx_status (status)
);

-- جدول معلومات المحلات الإضافية
CREATE TABLE vendor_profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    business_name VARCHAR(150) NOT NULL,
    commercial_register VARCHAR(50),
    address TEXT,
    city VARCHAR(50),
    district VARCHAR(50),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    working_hours TEXT,
    description TEXT,
    logo VARCHAR(255),
    rating DECIMAL(3, 2) DEFAULT 0.00,
    total_reviews INT DEFAULT 0,
    subscription_type ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic',
    subscription_expires DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_city (city),
    INDEX idx_rating (rating)
);

-- جدول طلبات قطع الغيار
CREATE TABLE part_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    car_make VARCHAR(50) NOT NULL,
    car_model VARCHAR(50) NOT NULL,
    car_year YEAR NOT NULL,
    part_name VARCHAR(100) NOT NULL,
    part_number VARCHAR(50),
    description TEXT,
    condition_type ENUM('new', 'used', 'both') DEFAULT 'both',
    urgency ENUM('low', 'medium', 'high') DEFAULT 'medium',
    budget_min DECIMAL(10, 2),
    budget_max DECIMAL(10, 2),
    location VARCHAR(100),
    images TEXT, -- JSON array of image filenames
    status ENUM('pending', 'active', 'closed', 'cancelled') DEFAULT 'pending',
    expires_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_customer_id (customer_id),
    INDEX idx_car_make (car_make),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- جدول العروض من المحلات
CREATE TABLE offers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_id INT NOT NULL,
    vendor_id INT NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    condition_type ENUM('new', 'used') NOT NULL,
    warranty_period INT DEFAULT 0, -- بالأشهر
    delivery_time VARCHAR(50),
    delivery_cost DECIMAL(10, 2) DEFAULT 0.00,
    notes TEXT,
    images TEXT, -- JSON array of image filenames
    status ENUM('pending', 'accepted', 'rejected', 'expired') DEFAULT 'pending',
    expires_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (request_id) REFERENCES part_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (vendor_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_request_id (request_id),
    INDEX idx_vendor_id (vendor_id),
    INDEX idx_status (status),
    INDEX idx_price (price)
);

-- جدول المعاملات المالية
CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    offer_id INT NOT NULL,
    customer_id INT NOT NULL,
    vendor_id INT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    commission DECIMAL(10, 2) NOT NULL,
    payment_method VARCHAR(50),
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    transaction_id VARCHAR(100),
    payment_gateway_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (vendor_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_offer_id (offer_id),
    INDEX idx_payment_status (payment_status)
);

-- جدول التقييمات والمراجعات
CREATE TABLE reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id INT NOT NULL,
    customer_id INT NOT NULL,
    vendor_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (vendor_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_vendor_id (vendor_id),
    INDEX idx_rating (rating)
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(150) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    related_id INT, -- ID of related record (request, offer, etc.)
    related_type VARCHAR(50), -- Type of related record
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);

-- جدول إعدادات النظام
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إدراج إعدادات افتراضية
INSERT INTO settings (setting_key, setting_value, description) VALUES
('commission_rate', '0.05', 'نسبة العمولة على كل معاملة'),
('request_expiry_days', '7', 'عدد أيام انتهاء صلاحية الطلب'),
('offer_expiry_days', '3', 'عدد أيام انتهاء صلاحية العرض'),
('max_offers_per_request', '10', 'الحد الأقصى للعروض لكل طلب'),
('site_maintenance', '0', 'وضع الصيانة للموقع'),
('email_notifications', '1', 'تفعيل الإشعارات بالإيميل'),
('sms_notifications', '1', 'تفعيل الإشعارات بالرسائل النصية');

-- إنشاء مستخدم إداري افتراضي
INSERT INTO users (name, email, phone, password, user_type, status, email_verified) VALUES
('مدير النظام', '<EMAIL>', '0501234567', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active', TRUE);
