-- إنشاء قاعدة بيانات موقع صناعية
-- Create database for Sanaeya website

CREATE DATABASE IF NOT EXISTS sanaeya_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE sanaeya_db;

-- جدول المستخدمين (العملاء والمحلات)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20) NOT NULL,
    password VARCHAR(255) NOT NULL,
    user_type ENUM('customer', 'vendor', 'admin') NOT NULL DEFAULT 'customer',
    status ENUM('active', 'inactive', 'suspended') NOT NULL DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    reset_token VARCHAR(255),
    reset_token_expires DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_user_type (user_type),
    INDEX idx_status (status)
);

-- جدول معلومات المحلات الإضافية
CREATE TABLE vendor_profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    business_name VARCHAR(150) NOT NULL,
    commercial_register VARCHAR(50),
    address TEXT,
    city VARCHAR(50),
    district VARCHAR(50),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    working_hours TEXT,
    description TEXT,
    logo VARCHAR(255),
    rating DECIMAL(3, 2) DEFAULT 0.00,
    total_reviews INT DEFAULT 0,
    subscription_type ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic',
    subscription_expires DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_city (city),
    INDEX idx_rating (rating)
);

-- جدول طلبات قطع الغيار
CREATE TABLE part_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    car_make VARCHAR(50) NOT NULL,
    car_model VARCHAR(50) NOT NULL,
    car_year YEAR NOT NULL,
    part_name VARCHAR(100) NOT NULL,
    part_number VARCHAR(50),
    description TEXT,
    condition_type ENUM('new', 'used', 'both') DEFAULT 'both',
    urgency ENUM('low', 'medium', 'high') DEFAULT 'medium',
    budget_min DECIMAL(10, 2),
    budget_max DECIMAL(10, 2),
    location VARCHAR(100),
    images TEXT, -- JSON array of image filenames
    status ENUM('pending', 'active', 'closed', 'cancelled') DEFAULT 'pending',
    expires_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_customer_id (customer_id),
    INDEX idx_car_make (car_make),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- جدول العروض من المحلات
CREATE TABLE offers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_id INT NOT NULL,
    vendor_id INT NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    condition_type ENUM('new', 'used') NOT NULL,
    warranty_period INT DEFAULT 0, -- بالأشهر
    delivery_time VARCHAR(50),
    delivery_cost DECIMAL(10, 2) DEFAULT 0.00,
    notes TEXT,
    images TEXT, -- JSON array of image filenames
    status ENUM('pending', 'accepted', 'rejected', 'expired') DEFAULT 'pending',
    expires_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (request_id) REFERENCES part_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (vendor_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_request_id (request_id),
    INDEX idx_vendor_id (vendor_id),
    INDEX idx_status (status),
    INDEX idx_price (price)
);

-- جدول المعاملات المالية
CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    offer_id INT NOT NULL,
    customer_id INT NOT NULL,
    vendor_id INT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    commission DECIMAL(10, 2) NOT NULL,
    payment_method VARCHAR(50),
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    transaction_id VARCHAR(100),
    payment_gateway_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (vendor_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_offer_id (offer_id),
    INDEX idx_payment_status (payment_status)
);

-- جدول التقييمات والمراجعات
CREATE TABLE reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id INT NOT NULL,
    customer_id INT NOT NULL,
    vendor_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (vendor_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_vendor_id (vendor_id),
    INDEX idx_rating (rating)
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(150) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    related_id INT, -- ID of related record (request, offer, etc.)
    related_type VARCHAR(50), -- Type of related record
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);

-- جدول إعدادات النظام
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إدراج إعدادات افتراضية
INSERT INTO settings (setting_key, setting_value, description) VALUES
('commission_rate', '0.05', 'نسبة العمولة على كل معاملة'),
('request_expiry_days', '7', 'عدد أيام انتهاء صلاحية الطلب'),
('offer_expiry_days', '3', 'عدد أيام انتهاء صلاحية العرض'),
('max_offers_per_request', '10', 'الحد الأقصى للعروض لكل طلب'),
('site_maintenance', '0', 'وضع الصيانة للموقع'),
('email_notifications', '1', 'تفعيل الإشعارات بالإيميل'),
('sms_notifications', '1', 'تفعيل الإشعارات بالرسائل النصية');

-- جدول العمولات
CREATE TABLE commissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    transaction_id INT NOT NULL,
    vendor_id INT NOT NULL,
    customer_id INT NOT NULL,
    offer_id INT NOT NULL,

    -- مبالغ العمولات
    transaction_amount DECIMAL(10,2) NOT NULL,
    vendor_commission_rate DECIMAL(5,2) DEFAULT 3.00,
    customer_commission_rate DECIMAL(5,2) DEFAULT 3.00,
    vendor_commission_amount DECIMAL(10,2) NOT NULL,
    customer_commission_amount DECIMAL(10,2) NOT NULL,
    total_commission DECIMAL(10,2) NOT NULL,

    -- حالة دفع العمولات
    vendor_payment_status ENUM('pending', 'paid', 'overdue', 'waived') DEFAULT 'pending',
    customer_payment_status ENUM('pending', 'paid', 'overdue', 'waived') DEFAULT 'pending',
    overall_status ENUM('pending', 'partial', 'completed') DEFAULT 'pending',

    -- تواريخ مهمة
    due_date DATE NOT NULL,
    vendor_paid_at TIMESTAMP NULL,
    customer_paid_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
    FOREIGN KEY (vendor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,

    INDEX idx_vendor_payment (vendor_id, vendor_payment_status),
    INDEX idx_customer_payment (customer_id, customer_payment_status),
    INDEX idx_due_date (due_date),
    INDEX idx_overall_status (overall_status)
);

-- جدول مدفوعات العمولات
CREATE TABLE commission_payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    commission_id INT NOT NULL,
    payer_id INT NOT NULL,
    payer_type ENUM('vendor', 'customer') NOT NULL,

    -- تفاصيل الدفعة
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('bank_transfer', 'cash', 'check', 'online', 'credit_adjustment') NOT NULL,
    payment_reference VARCHAR(100),

    -- معلومات إضافية
    notes TEXT,
    receipt_image VARCHAR(255),
    verified_by INT NULL,
    verified_at TIMESTAMP NULL,

    -- حالة الدفعة
    status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (commission_id) REFERENCES commissions(id) ON DELETE CASCADE,
    FOREIGN KEY (payer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL,

    INDEX idx_payer (payer_id, payer_type),
    INDEX idx_status (status),
    INDEX idx_payment_date (created_at)
);

-- جدول إعدادات العمولات
CREATE TABLE commission_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_name VARCHAR(50) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- إدراج إعدادات العمولات الافتراضية
INSERT INTO commission_settings (setting_name, setting_value, description) VALUES
('vendor_commission_rate', '3.00', 'نسبة عمولة المحل من كل صفقة (%)'),
('customer_commission_rate', '3.00', 'نسبة عمولة العميل من كل صفقة (%)'),
('payment_due_days', '30', 'عدد الأيام المسموحة لدفع العمولة'),
('overdue_grace_days', '7', 'عدد أيام السماح بعد تاريخ الاستحقاق'),
('minimum_commission', '5.00', 'أقل مبلغ عمولة (ريال)'),
('auto_reminders', '1', 'تفعيل التذكيرات التلقائية (1=نعم، 0=لا)'),
('reminder_days_before', '3', 'عدد الأيام قبل الاستحقاق لإرسال تذكير'),
('late_fee_rate', '0.50', 'نسبة رسوم التأخير اليومية (%)');

-- جدول تذكيرات العمولات
CREATE TABLE commission_reminders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    commission_id INT NOT NULL,
    user_id INT NOT NULL,
    user_type ENUM('vendor', 'customer') NOT NULL,

    -- نوع التذكير
    reminder_type ENUM('due_soon', 'overdue', 'final_notice') NOT NULL,

    -- تفاصيل التذكير
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    method ENUM('email', 'sms', 'notification') NOT NULL,
    message TEXT,

    -- حالة التذكير
    status ENUM('sent', 'delivered', 'failed') DEFAULT 'sent',

    FOREIGN KEY (commission_id) REFERENCES commissions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_user_reminder (user_id, user_type),
    INDEX idx_reminder_type (reminder_type),
    INDEX idx_sent_date (sent_at)
);

-- جدول المحادثات
CREATE TABLE conversations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    offer_id INT NOT NULL,
    customer_id INT NOT NULL,
    vendor_id INT NOT NULL,

    -- حالة المحادثة
    status ENUM('active', 'closed', 'archived') DEFAULT 'active',

    -- آخر رسالة
    last_message_id INT NULL,
    last_message_at TIMESTAMP NULL,

    -- عدادات غير المقروءة
    customer_unread_count INT DEFAULT 0,
    vendor_unread_count INT DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (vendor_id) REFERENCES users(id) ON DELETE CASCADE,

    UNIQUE KEY unique_conversation (offer_id),
    INDEX idx_customer (customer_id, status),
    INDEX idx_vendor (vendor_id, status),
    INDEX idx_last_message (last_message_at)
);

-- جدول الرسائل
CREATE TABLE messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    conversation_id INT NOT NULL,
    sender_id INT NOT NULL,
    sender_type ENUM('customer', 'vendor') NOT NULL,

    -- محتوى الرسالة
    message_type ENUM('text', 'image', 'file', 'system') DEFAULT 'text',
    content TEXT NOT NULL,
    attachment VARCHAR(255) NULL,

    -- حالة الرسالة
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    is_deleted BOOLEAN DEFAULT FALSE,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_conversation (conversation_id, created_at),
    INDEX idx_sender (sender_id, sender_type),
    INDEX idx_unread (conversation_id, is_read)
);

-- جدول التنبيهات
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,

    -- نوع التنبيه
    type ENUM('new_offer', 'offer_accepted', 'offer_rejected', 'new_message', 'commission_due', 'commission_overdue', 'new_request', 'request_closed', 'system_announcement') NOT NULL,

    -- محتوى التنبيه
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,

    -- بيانات إضافية (JSON)
    data JSON NULL,

    -- روابط
    action_url VARCHAR(500) NULL,

    -- حالة التنبيه
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,

    -- أولوية التنبيه
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_user_unread (user_id, is_read, created_at),
    INDEX idx_type (type),
    INDEX idx_priority (priority),
    INDEX idx_expires (expires_at)
);

-- جدول إعدادات التنبيهات للمستخدمين
CREATE TABLE notification_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,

    -- إعدادات التنبيهات
    email_notifications BOOLEAN DEFAULT TRUE,
    sms_notifications BOOLEAN DEFAULT FALSE,
    push_notifications BOOLEAN DEFAULT TRUE,

    -- أنواع التنبيهات المفعلة
    new_offers BOOLEAN DEFAULT TRUE,
    offer_updates BOOLEAN DEFAULT TRUE,
    new_messages BOOLEAN DEFAULT TRUE,
    commission_reminders BOOLEAN DEFAULT TRUE,
    system_announcements BOOLEAN DEFAULT TRUE,

    -- أوقات عدم الإزعاج
    quiet_hours_start TIME NULL,
    quiet_hours_end TIME NULL,

    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_settings (user_id)
);

-- إدراج إعدادات التنبيهات الافتراضية للمستخدمين الموجودين
INSERT INTO notification_settings (user_id)
SELECT id FROM users ON DUPLICATE KEY UPDATE user_id = user_id;

-- جدول سجل التنبيهات المرسلة
CREATE TABLE notification_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    notification_id INT NOT NULL,

    -- طريقة الإرسال
    method ENUM('email', 'sms', 'push', 'in_app') NOT NULL,

    -- حالة الإرسال
    status ENUM('pending', 'sent', 'delivered', 'failed') DEFAULT 'pending',

    -- تفاصيل الإرسال
    recipient VARCHAR(255) NOT NULL,
    response_data TEXT NULL,
    error_message TEXT NULL,

    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE,

    INDEX idx_notification (notification_id),
    INDEX idx_status (status),
    INDEX idx_method (method)
);

-- إنشاء مستخدم إداري افتراضي (كلمة المرور: admin123)
INSERT INTO users (name, email, phone, password, user_type, status, email_verified) VALUES
('مدير النظام', '<EMAIL>', '0501234567', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'admin', 'active', TRUE);
