-- تحديث قاعدة البيانات لحل تضارب جدول التنبيهات
-- يجب تشغيل هذا الملف إذا كان جدول notifications موجود مسبقاً

-- إنشاء نسخة احتياطية من الجدول القديم إذا كان موجوداً
CREATE TABLE IF NOT EXISTS old_notifications_backup AS SELECT * FROM notifications;

-- حذف الجدول القديم
DROP TABLE IF EXISTS notifications;

-- إنشاء جدول التنبيهات الجديد
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    
    -- نوع التنبيه
    type ENUM('new_offer', 'offer_accepted', 'offer_rejected', 'new_message', 'commission_due', 'commission_overdue', 'new_request', 'request_closed', 'system_announcement') NOT NULL,
    
    -- محتوى التنبيه
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    
    -- بيانات إضافية (JSON)
    data JSON NULL,
    
    -- روابط
    action_url VARCHAR(500) NULL,
    
    -- حالة التنبيه
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    
    -- أولوية التنبيه
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_user_unread (user_id, is_read, created_at),
    INDEX idx_type (type),
    INDEX idx_priority (priority),
    INDEX idx_expires (expires_at)
);

-- إنشاء جدول إعدادات التنبيهات إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS notification_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    
    -- إعدادات التنبيهات
    email_notifications BOOLEAN DEFAULT TRUE,
    sms_notifications BOOLEAN DEFAULT FALSE,
    push_notifications BOOLEAN DEFAULT TRUE,
    
    -- أنواع التنبيهات المفعلة
    new_offers BOOLEAN DEFAULT TRUE,
    offer_updates BOOLEAN DEFAULT TRUE,
    new_messages BOOLEAN DEFAULT TRUE,
    commission_reminders BOOLEAN DEFAULT TRUE,
    system_announcements BOOLEAN DEFAULT TRUE,
    
    -- أوقات عدم الإزعاج
    quiet_hours_start TIME NULL,
    quiet_hours_end TIME NULL,
    
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_settings (user_id)
);

-- إنشاء جدول سجل التنبيهات إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS notification_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    notification_id INT NOT NULL,
    
    -- طريقة الإرسال
    method ENUM('email', 'sms', 'push', 'in_app') NOT NULL,
    
    -- حالة الإرسال
    status ENUM('pending', 'sent', 'delivered', 'failed') DEFAULT 'pending',
    
    -- تفاصيل الإرسال
    recipient VARCHAR(255) NOT NULL,
    response_data TEXT NULL,
    error_message TEXT NULL,
    
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE,
    
    INDEX idx_notification (notification_id),
    INDEX idx_status (status),
    INDEX idx_method (method)
);

-- إدراج إعدادات التنبيهات الافتراضية للمستخدمين الموجودين
INSERT IGNORE INTO notification_settings (user_id) 
SELECT id FROM users;

-- إنشاء تنبيه ترحيبي للمستخدمين الموجودين
INSERT INTO notifications (user_id, type, title, message, priority) 
SELECT id, 'system_announcement', 'مرحباً بك في نظام التنبيهات الجديد!', 
'تم تحديث نظام التنبيهات ليوفر لك تجربة أفضل في متابعة العروض والرسائل.', 'normal'
FROM users 
WHERE id NOT IN (SELECT DISTINCT user_id FROM notifications WHERE type = 'system_announcement' AND title LIKE '%نظام التنبيهات الجديد%');
