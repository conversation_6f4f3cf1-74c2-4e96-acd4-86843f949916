<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

session_start();

echo "<h1>تشخيص مشاكل التنبيهات</h1>";

try {
    $db = getDB();
    
    // فحص وجود جدول notifications
    echo "<h2>فحص جدول التنبيهات:</h2>";
    
    try {
        $result = $db->query("DESCRIBE notifications")->fetchAll();
        echo "<p style='color: green;'>✅ جدول notifications موجود</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($result as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ جدول notifications غير موجود: " . $e->getMessage() . "</p>";
        
        // إنشاء الجدول
        echo "<p>محاولة إنشاء الجدول...</p>";
        try {
            $db->getConnection()->exec("
                CREATE TABLE notifications (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    type ENUM('new_offer', 'offer_accepted', 'offer_rejected', 'new_message', 'commission_due', 'commission_overdue', 'new_request', 'request_closed', 'system_announcement') NOT NULL,
                    title VARCHAR(255) NOT NULL,
                    message TEXT NOT NULL,
                    data JSON NULL,
                    action_url VARCHAR(500) NULL,
                    is_read BOOLEAN DEFAULT FALSE,
                    read_at TIMESTAMP NULL,
                    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    INDEX idx_user_unread (user_id, is_read, created_at),
                    INDEX idx_type (type),
                    INDEX idx_priority (priority)
                )
            ");
            echo "<p style='color: green;'>✅ تم إنشاء جدول notifications بنجاح</p>";
        } catch (Exception $e2) {
            echo "<p style='color: red;'>❌ فشل في إنشاء الجدول: " . $e2->getMessage() . "</p>";
        }
    }
    
    // فحص وجود جدول notification_settings
    echo "<h2>فحص جدول إعدادات التنبيهات:</h2>";
    
    try {
        $result = $db->query("SELECT COUNT(*) as count FROM notification_settings")->fetch();
        echo "<p style='color: green;'>✅ جدول notification_settings موجود ويحتوي على {$result['count']} سجل</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ جدول notification_settings غير موجود: " . $e->getMessage() . "</p>";
        
        // إنشاء الجدول
        try {
            $db->getConnection()->exec("
                CREATE TABLE notification_settings (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    email_notifications BOOLEAN DEFAULT TRUE,
                    sms_notifications BOOLEAN DEFAULT FALSE,
                    push_notifications BOOLEAN DEFAULT TRUE,
                    new_offers BOOLEAN DEFAULT TRUE,
                    offer_updates BOOLEAN DEFAULT TRUE,
                    new_messages BOOLEAN DEFAULT TRUE,
                    commission_reminders BOOLEAN DEFAULT TRUE,
                    system_announcements BOOLEAN DEFAULT TRUE,
                    quiet_hours_start TIME NULL,
                    quiet_hours_end TIME NULL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_user_settings (user_id)
                )
            ");
            echo "<p style='color: green;'>✅ تم إنشاء جدول notification_settings بنجاح</p>";
            
            // إضافة إعدادات للمستخدمين الموجودين
            $db->getConnection()->exec("INSERT IGNORE INTO notification_settings (user_id) SELECT id FROM users");
            echo "<p style='color: green;'>✅ تم إضافة إعدادات للمستخدمين الموجودين</p>";
            
        } catch (Exception $e2) {
            echo "<p style='color: red;'>❌ فشل في إنشاء جدول notification_settings: " . $e2->getMessage() . "</p>";
        }
    }
    
    // اختبار إنشاء تنبيه بسيط
    echo "<h2>اختبار إنشاء تنبيه:</h2>";
    
    if (isLoggedIn()) {
        $user_id = $_SESSION['user_id'];
        echo "<p>المستخدم الحالي: {$_SESSION['user_name']} (ID: $user_id)</p>";
        
        try {
            // إدراج مباشر في قاعدة البيانات
            $stmt = $db->query(
                "INSERT INTO notifications (user_id, type, title, message, priority) VALUES (?, ?, ?, ?, ?)",
                [$user_id, 'system_announcement', 'اختبار مباشر', 'هذا تنبيه تجريبي مباشر', 'normal']
            );
            
            if ($stmt) {
                $notification_id = $db->getConnection()->lastInsertId();
                echo "<p style='color: green;'>✅ تم إنشاء تنبيه مباشر بنجاح (ID: $notification_id)</p>";
                
                // اختبار جلب التنبيه
                $notification = $db->query("SELECT * FROM notifications WHERE id = ?", [$notification_id])->fetch();
                if ($notification) {
                    echo "<p style='color: green;'>✅ تم جلب التنبيه بنجاح</p>";
                    echo "<pre>" . print_r($notification, true) . "</pre>";
                } else {
                    echo "<p style='color: red;'>❌ فشل في جلب التنبيه</p>";
                }
                
            } else {
                echo "<p style='color: red;'>❌ فشل في إنشاء التنبيه المباشر</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في إنشاء التنبيه المباشر: " . $e->getMessage() . "</p>";
        }
        
        // اختبار الدالة
        echo "<h3>اختبار دالة createNotification:</h3>";
        try {
            $notification_id = createNotification(
                $user_id,
                'system_announcement',
                'اختبار الدالة',
                'هذا تنبيه من خلال الدالة',
                null,
                null,
                'normal'
            );
            
            if ($notification_id && $notification_id > 0) {
                echo "<p style='color: green;'>✅ دالة createNotification تعمل بنجاح (ID: $notification_id)</p>";
            } else {
                echo "<p style='color: red;'>❌ دالة createNotification فشلت (returned: $notification_id)</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في دالة createNotification: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠️ يجب تسجيل الدخول لاختبار التنبيهات</p>";
        echo "<p><a href='login.php'>تسجيل الدخول</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ عام: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>العودة للرئيسية</a> | <a href='quick-fix.php'>الإصلاح السريع</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
p { margin: 10px 0; }
table { margin: 10px 0; }
th, td { padding: 5px 10px; text-align: left; }
th { background: #f0f0f0; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
