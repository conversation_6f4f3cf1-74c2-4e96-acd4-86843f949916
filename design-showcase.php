<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

$page_title = 'معرض التصميم الجديد';
include 'includes/header.php';
?>

<style>
/* تحسينات إضافية للعرض */
.showcase-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.feature-showcase {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin: 1rem 0;
    transition: all 0.3s ease;
}

.feature-showcase:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.2);
}

.gradient-text {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.demo-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 20px;
    overflow: hidden;
}

.demo-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.demo-button {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.demo-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.2);
    color: white;
}

.demo-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.demo-button:hover::before {
    left: 100%;
}

.stats-counter {
    font-size: 3rem;
    font-weight: 700;
    color: #667eea;
}

.floating-element {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
</style>

<!-- قسم العرض الرئيسي -->
<section class="showcase-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 animate__animated animate__fadeInLeft">
                <h1 class="display-3 fw-bold mb-4">
                    التصميم الجديد لموقع <span class="text-warning">صناعية</span>
                </h1>
                <p class="lead mb-4">
                    تجربة مستخدم محسنة مع تصميم عصري وتأثيرات بصرية جذابة
                </p>
                <div class="d-flex gap-3 justify-content-center justify-content-lg-start">
                    <button class="demo-button" onclick="showDemo('colors')">
                        <i class="fas fa-palette me-2"></i>الألوان
                    </button>
                    <button class="demo-button" onclick="showDemo('animations')">
                        <i class="fas fa-magic me-2"></i>التأثيرات
                    </button>
                    <button class="demo-button" onclick="showDemo('components')">
                        <i class="fas fa-cubes me-2"></i>المكونات
                    </button>
                </div>
            </div>
            <div class="col-lg-6 text-center animate__animated animate__fadeInRight">
                <div class="floating-element">
                    <i class="fas fa-laptop-code display-1 text-warning mb-4"></i>
                </div>
                <h3 class="fw-bold">تصميم متجاوب وحديث</h3>
                <p>يعمل بشكل مثالي على جميع الأجهزة</p>
            </div>
        </div>
    </div>
</section>

<!-- قسم الميزات -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold gradient-text mb-3">الميزات الجديدة</h2>
            <p class="lead text-muted">تحسينات شاملة لتجربة المستخدم</p>
        </div>
        
        <div class="row g-4">
            <div class="col-md-4">
                <div class="feature-showcase text-center">
                    <i class="fas fa-paint-brush display-4 text-warning mb-3"></i>
                    <h4 class="fw-bold">تصميم عصري</h4>
                    <p>ألوان متدرجة وتأثيرات بصرية جذابة</p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="feature-showcase text-center">
                    <i class="fas fa-mobile-alt display-4 text-info mb-3"></i>
                    <h4 class="fw-bold">متجاوب تماماً</h4>
                    <p>يعمل بشكل مثالي على جميع الأجهزة</p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="feature-showcase text-center">
                    <i class="fas fa-rocket display-4 text-success mb-3"></i>
                    <h4 class="fw-bold">أداء سريع</h4>
                    <p>تحميل سريع وتفاعل سلس</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم العرض التفاعلي -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold text-primary mb-3">عرض تفاعلي</h2>
            <p class="lead text-muted">جرب الميزات الجديدة</p>
        </div>
        
        <div class="row g-4">
            <!-- بطاقة تجريبية -->
            <div class="col-md-6 col-lg-4">
                <div class="demo-card card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-star me-2"></i>
                            بطاقة تجريبية
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">هذه بطاقة تجريبية تظهر التصميم الجديد مع التأثيرات المحسنة.</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-success">جديد</span>
                            <button class="btn btn-outline-primary btn-sm">تجربة</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- نموذج تجريبي -->
            <div class="col-md-6 col-lg-4">
                <div class="demo-card card h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-edit me-2"></i>
                            نموذج تجريبي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold">حقل تجريبي</label>
                            <input type="text" class="form-control" placeholder="اكتب شيئاً...">
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">قائمة منسدلة</label>
                            <select class="form-select">
                                <option>خيار 1</option>
                                <option>خيار 2</option>
                                <option>خيار 3</option>
                            </select>
                        </div>
                        <button class="btn btn-success w-100">إرسال</button>
                    </div>
                </div>
            </div>
            
            <!-- إحصائيات -->
            <div class="col-md-6 col-lg-4">
                <div class="demo-card card h-100">
                    <div class="card-header bg-warning text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            إحصائيات
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="stats-counter pulse-animation" data-target="1250">0</div>
                        <p class="text-muted mb-3">عدد المستخدمين</p>
                        
                        <div class="progress mb-3">
                            <div class="progress-bar bg-warning" style="width: 75%"></div>
                        </div>
                        
                        <small class="text-muted">75% نمو هذا الشهر</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم الأزرار التجريبية -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold text-primary mb-3">الأزرار والتأثيرات</h2>
            <p class="lead text-muted">مجموعة متنوعة من الأزرار مع تأثيرات جذابة</p>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="d-flex flex-wrap gap-3 justify-content-center">
                    <button class="btn btn-primary btn-lg">أساسي</button>
                    <button class="btn btn-success btn-lg">نجاح</button>
                    <button class="btn btn-warning btn-lg">تحذير</button>
                    <button class="btn btn-danger btn-lg">خطر</button>
                    <button class="btn btn-info btn-lg">معلومات</button>
                    <button class="btn btn-outline-primary btn-lg">محدد</button>
                    <button class="btn btn-outline-success btn-lg">محدد نجاح</button>
                </div>
                
                <div class="text-center mt-4">
                    <p class="text-muted">جرب تمرير الماوس فوق الأزرار لرؤية التأثيرات</p>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// تحريك العدادات
function animateCounter(element) {
    const target = parseInt(element.getAttribute('data-target'));
    const duration = 2000;
    const increment = target / (duration / 16);
    let current = 0;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current).toLocaleString('ar-SA');
    }, 16);
}

// تشغيل العدادات عند التمرير
const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            animateCounter(entry.target);
            observer.unobserve(entry.target);
        }
    });
});

document.querySelectorAll('.stats-counter').forEach(counter => {
    observer.observe(counter);
});

// دالة عرض التجارب
function showDemo(type) {
    let message = '';
    switch(type) {
        case 'colors':
            message = 'تم تطبيق نظام ألوان متدرج جديد مع متغيرات CSS للتحكم السهل';
            break;
        case 'animations':
            message = 'تأثيرات CSS3 وJavaScript متقدمة لتجربة مستخدم سلسة';
            break;
        case 'components':
            message = 'مكونات Bootstrap محسنة مع تخصيصات إضافية';
            break;
    }
    
    if (typeof SanaeyaApp !== 'undefined' && SanaeyaApp.showToast) {
        SanaeyaApp.showToast(message, 'info');
    } else {
        alert(message);
    }
}

// تأثيرات إضافية عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    // تأثير الظهور التدريجي للعناصر
    const elements = document.querySelectorAll('.feature-showcase, .demo-card');
    elements.forEach((element, index) => {
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 200);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
