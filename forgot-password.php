<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// إعادة توجيه المستخدمين المسجلين
if (isLoggedIn()) {
    redirect('index.php');
}

$errors = [];
$success = false;

if ($_POST) {
    $email = sanitize($_POST['email'] ?? '');
    
    if (empty($email) || !isValidEmail($email)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    } else {
        $db = getDB();
        $stmt = $db->query("SELECT id, name FROM users WHERE email = ? AND status = 'active'", [$email]);
        $user = $stmt->fetch();
        
        if ($user) {
            // إنشاء رمز إعادة تعيين كلمة المرور
            $reset_token = generateToken();
            $expires = date('Y-m-d H:i:s', strtotime('+1 hour')); // ينتهي خلال ساعة
            
            // حفظ الرمز في قاعدة البيانات
            $db->query(
                "UPDATE users SET reset_token = ?, reset_token_expires = ? WHERE id = ?",
                [$reset_token, $expires, $user['id']]
            );
            
            // إرسال الإيميل (محاكاة)
            $reset_link = SITE_URL . "/reset-password.php?token=" . $reset_token;
            // sendEmail($email, "إعادة تعيين كلمة المرور - " . SITE_NAME, "يرجى النقر على الرابط لإعادة تعيين كلمة المرور: " . $reset_link);
            
            $success = true;
        } else {
            // لأغراض الأمان، نعرض نفس الرسالة حتى لو لم يكن الإيميل موجود
            $success = true;
        }
    }
}

$page_title = 'نسيت كلمة المرور';
include 'includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-header bg-warning text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-key me-2"></i>
                    نسيت كلمة المرور
                </h4>
            </div>
            <div class="card-body p-4">
                <?php if ($success): ?>
                    <div class="alert alert-success text-center">
                        <i class="fas fa-check-circle fs-2 mb-3"></i>
                        <h5>تم إرسال رابط إعادة التعيين!</h5>
                        <p class="mb-0">
                            إذا كان البريد الإلكتروني مسجل لدينا، ستصلك رسالة تحتوي على رابط إعادة تعيين كلمة المرور.
                            يرجى التحقق من صندوق الوارد وصندوق الرسائل غير المرغوب فيها.
                        </p>
                        <hr>
                        <a href="login.php" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            العودة لتسجيل الدخول
                        </a>
                    </div>
                <?php else: ?>
                    <div class="text-center mb-4">
                        <i class="fas fa-lock fs-1 text-warning mb-3"></i>
                        <p class="text-muted">
                            أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة تعيين كلمة المرور
                        </p>
                    </div>

                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email" name="email" class="form-control" value="<?php echo $_POST['email'] ?? ''; ?>" required>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-warning w-100 mb-3">
                            <i class="fas fa-paper-plane me-2"></i>
                            إرسال رابط إعادة التعيين
                        </button>
                    </form>

                    <div class="text-center">
                        <p class="mb-0">تذكرت كلمة المرور؟ <a href="login.php">تسجيل الدخول</a></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
