    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5 class="mb-3">
                        <i class="fas fa-car me-2"></i>
                        <?php echo SITE_NAME; ?>
                    </h5>
                    <p class="text-muted">
                        منصة رائدة لربط العملاء بمحلات قطع غيار السيارات، 
                        نوفر لك أفضل العروض وأسرع الخدمات.
                    </p>
                </div>
                
                <div class="col-md-2">
                    <h6 class="mb-3">روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-muted text-decoration-none">الرئيسية</a></li>
                        <li><a href="request-part.php" class="text-muted text-decoration-none">طلب قطعة غيار</a></li>
                        <li><a href="about.php" class="text-muted text-decoration-none">من نحن</a></li>
                        <li><a href="contact.php" class="text-muted text-decoration-none">اتصل بنا</a></li>
                    </ul>
                </div>
                
                <div class="col-md-2">
                    <h6 class="mb-3">الحساب</h6>
                    <ul class="list-unstyled">
                        <?php if (isLoggedIn()): ?>
                            <li><a href="profile.php" class="text-muted text-decoration-none">الملف الشخصي</a></li>
                            <?php if (getUserType() == 'customer'): ?>
                                <li><a href="customer/dashboard.php" class="text-muted text-decoration-none">لوحة التحكم</a></li>
                            <?php elseif (getUserType() == 'vendor'): ?>
                                <li><a href="vendor/dashboard.php" class="text-muted text-decoration-none">لوحة التحكم</a></li>
                            <?php endif; ?>
                            <li><a href="logout.php" class="text-muted text-decoration-none">تسجيل الخروج</a></li>
                        <?php else: ?>
                            <li><a href="login.php" class="text-muted text-decoration-none">تسجيل الدخول</a></li>
                            <li><a href="register.php" class="text-muted text-decoration-none">إنشاء حساب</a></li>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <div class="col-md-4">
                    <h6 class="mb-3">تواصل معنا</h6>
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-envelope me-2"></i>
                        <span class="text-muted"><?php echo SITE_EMAIL; ?></span>
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-phone me-2"></i>
                        <span class="text-muted">+966 50 123 4567</span>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        <span class="text-muted">الرياض، المملكة العربية السعودية</span>
                    </div>
                    
                    <!-- Social Media -->
                    <div class="d-flex">
                        <a href="#" class="text-muted me-3 fs-5"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-muted me-3 fs-5"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-muted me-3 fs-5"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-muted me-3 fs-5"><i class="fab fa-whatsapp"></i></a>
                    </div>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">
                        &copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="privacy.php" class="text-muted text-decoration-none me-3">سياسة الخصوصية</a>
                    <a href="terms.php" class="text-muted text-decoration-none">شروط الاستخدام</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // تأكيد الحذف
        function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
            return confirm(message);
        }
        
        // إخفاء التنبيهات تلقائياً
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA');
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }
        
        // تحديث الوقت كل دقيقة
        setInterval(updateTime, 60000);
        updateTime();
    </script>
</body>
</html>
