<?php
/**
 * دوال مساعدة عامة لموقع صناعية
 * General helper functions for Sanaeya website
 */

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

/**
 * تنظيف البيانات المدخلة
 */
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * التحقق من تسجيل الدخول
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * التحقق من نوع المستخدم
 */
function getUserType() {
    return $_SESSION['user_type'] ?? null;
}

/**
 * إعادة التوجيه
 */
function redirect($url) {
    header("Location: " . $url);
    exit();
}

/**
 * عرض رسالة تنبيه
 */
function showAlert($message, $type = 'info') {
    $_SESSION['alert'] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * الحصول على رسالة التنبيه وحذفها
 */
function getAlert() {
    if (isset($_SESSION['alert'])) {
        $alert = $_SESSION['alert'];
        unset($_SESSION['alert']);
        return $alert;
    }
    return null;
}

/**
 * تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * إنشاء رمز عشوائي
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * تنسيق التاريخ بالعربية
 */
function formatArabicDate($date) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[(int)date('m', $timestamp)];
    $year = date('Y', $timestamp);
    
    return "$day $month $year";
}

/**
 * تنسيق السعر
 */
function formatPrice($price) {
    return number_format($price, 2) . ' ريال';
}

/**
 * رفع الملفات
 */
function uploadFile($file, $directory = 'uploads/') {
    if (!isset($file['error']) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, ALLOWED_EXTENSIONS)) {
        return false;
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return false;
    }
    
    $filename = uniqid() . '.' . $extension;
    $filepath = $directory . $filename;
    
    if (!is_dir($directory)) {
        mkdir($directory, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return $filename;
    }
    
    return false;
}

/**
 * إرسال إيميل
 */
function sendEmail($to, $subject, $message) {
    $headers = "From: " . SITE_EMAIL . "\r\n";
    $headers .= "Reply-To: " . SITE_EMAIL . "\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    
    return mail($to, $subject, $message, $headers);
}

/**
 * التحقق من صحة الإيميل
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * التحقق من صحة رقم الهاتف السعودي
 */
function isValidSaudiPhone($phone) {
    $pattern = '/^(05|5)[0-9]{8}$/';
    return preg_match($pattern, $phone);
}

/**
 * حماية من CSRF
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = generateToken();
    }
    return $_SESSION['csrf_token'];
}

function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * تسجيل الأخطاء
 */
function logError($message) {
    error_log(date('Y-m-d H:i:s') . " - " . $message . "\n", 3, "logs/error.log");
}

// دوال العمولات
function createCommission($transaction_id, $vendor_id, $customer_id, $offer_id, $transaction_amount) {
    $db = getDB();

    // جلب إعدادات العمولة
    $settings = getCommissionSettings();

    $vendor_rate = floatval($settings['vendor_commission_rate']);
    $customer_rate = floatval($settings['customer_commission_rate']);
    $due_days = intval($settings['payment_due_days']);
    $min_commission = floatval($settings['minimum_commission']);

    // حساب العمولات
    $vendor_commission = max($transaction_amount * ($vendor_rate / 100), $min_commission);
    $customer_commission = max($transaction_amount * ($customer_rate / 100), $min_commission);
    $total_commission = $vendor_commission + $customer_commission;

    // تاريخ الاستحقاق
    $due_date = date('Y-m-d', strtotime("+{$due_days} days"));

    // إنشاء العمولة
    $db->query(
        "INSERT INTO commissions (transaction_id, vendor_id, customer_id, offer_id, transaction_amount,
         vendor_commission_rate, customer_commission_rate, vendor_commission_amount, customer_commission_amount,
         total_commission, due_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        [
            $transaction_id, $vendor_id, $customer_id, $offer_id, $transaction_amount,
            $vendor_rate, $customer_rate, $vendor_commission, $customer_commission,
            $total_commission, $due_date
        ]
    );

    return $db->getConnection()->lastInsertId();
}

function getCommissionSettings() {
    $db = getDB();
    $settings_raw = $db->query("SELECT setting_name, setting_value FROM commission_settings")->fetchAll();

    $settings = [];
    foreach ($settings_raw as $setting) {
        $settings[$setting['setting_name']] = $setting['setting_value'];
    }

    return $settings;
}

function updateCommissionSettings($settings) {
    $db = getDB();

    foreach ($settings as $name => $value) {
        $db->query(
            "UPDATE commission_settings SET setting_value = ?, updated_by = ?, updated_at = NOW() WHERE setting_name = ?",
            [$value, $_SESSION['user_id'], $name]
        );
    }

    return true;
}

function checkOverdueCommissions() {
    $db = getDB();

    // تحديث العمولات المتأخرة للمحلات
    $db->query(
        "UPDATE commissions SET vendor_payment_status = 'overdue'
         WHERE vendor_payment_status = 'pending' AND due_date < CURDATE()"
    );

    // تحديث العمولات المتأخرة للعملاء
    $db->query(
        "UPDATE commissions SET customer_payment_status = 'overdue'
         WHERE customer_payment_status = 'pending' AND due_date < CURDATE()"
    );

    return true;
}

function sendCommissionReminder($commission_id, $user_id, $user_type, $reminder_type) {
    $db = getDB();

    // جلب تفاصيل العمولة
    $commission = $db->query(
        "SELECT c.*, u.name, u.email, u.phone FROM commissions c
         JOIN users u ON (c.vendor_id = u.id OR c.customer_id = u.id)
         WHERE c.id = ? AND u.id = ?",
        [$commission_id, $user_id]
    )->fetch();

    if (!$commission) return false;

    // إنشاء رسالة التذكير
    $messages = [
        'due_soon' => 'تذكير: عمولة خدمة مستحقة خلال 3 أيام',
        'overdue' => 'تنبيه: عمولة خدمة متأخرة',
        'final_notice' => 'إشعار أخير: عمولة خدمة متأخرة'
    ];

    $message = $messages[$reminder_type] ?? 'تذكير بعمولة خدمة';

    // تسجيل التذكير
    $db->query(
        "INSERT INTO commission_reminders (commission_id, user_id, user_type, reminder_type, method, message)
         VALUES (?, ?, ?, ?, 'email', ?)",
        [$commission_id, $user_id, $user_type, $reminder_type, $message]
    );

    // إرسال الإيميل (محاكاة)
    // sendEmail($commission['email'], $message, "تفاصيل العمولة...");

    return true;
}

function getCommissionStats($user_id = null, $user_type = null) {
    $db = getDB();

    $where_clause = "1=1";
    $params = [];

    if ($user_id && $user_type) {
        if ($user_type === 'vendor') {
            $where_clause = "vendor_id = ?";
            $params[] = $user_id;
        } elseif ($user_type === 'customer') {
            $where_clause = "customer_id = ?";
            $params[] = $user_id;
        }
    }

    return $db->query("SELECT
        COUNT(*) as total_commissions,
        COUNT(CASE WHEN overall_status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN overall_status = 'partial' THEN 1 END) as partial,
        COUNT(CASE WHEN overall_status = 'completed' THEN 1 END) as completed,
        COALESCE(SUM(total_commission), 0) as total_amount,
        COALESCE(SUM(CASE WHEN overall_status = 'completed' THEN total_commission ELSE 0 END), 0) as collected_amount
        FROM commissions WHERE $where_clause", $params)->fetch();
}

// دوال التنبيهات
function createNotification($user_id, $type, $title, $message, $data = null, $action_url = null, $priority = 'normal') {
    $db = getDB();

    // تحويل البيانات إلى JSON
    $data_json = $data ? json_encode($data) : null;

    // إنشاء التنبيه
    $stmt = $db->query(
        "INSERT INTO notifications (user_id, type, title, message, data, action_url, priority)
         VALUES (?, ?, ?, ?, ?, ?, ?)",
        [$user_id, $type, $title, $message, $data_json, $action_url, $priority]
    );

    if (!$stmt) {
        error_log("Failed to create notification for user $user_id");
        return false;
    }

    $notification_id = $db->getConnection()->lastInsertId();

    if ($notification_id > 0) {
        // إرسال التنبيه عبر الطرق المختلفة
        sendNotification($notification_id, $user_id);
    }

    return $notification_id;
}

function sendNotification($notification_id, $user_id) {
    $db = getDB();

    // التحقق من صحة المعاملات
    if (!$notification_id || $notification_id <= 0) {
        error_log("Invalid notification_id: $notification_id");
        return false;
    }

    // جلب إعدادات التنبيهات للمستخدم
    $settings_stmt = $db->query(
        "SELECT * FROM notification_settings WHERE user_id = ?",
        [$user_id]
    );

    if (!$settings_stmt) {
        error_log("Failed to query notification settings for user $user_id");
        return false;
    }

    $settings = $settings_stmt->fetch();

    if (!$settings) {
        // إنشاء إعدادات افتراضية
        $db->query(
            "INSERT INTO notification_settings (user_id) VALUES (?)",
            [$user_id]
        );
        $settings = [
            'email_notifications' => true,
            'sms_notifications' => false,
            'push_notifications' => true
        ];
    }

    // جلب تفاصيل التنبيه
    $notification_stmt = $db->query(
        "SELECT n.*, u.email, u.phone FROM notifications n
         JOIN users u ON n.user_id = u.id
         WHERE n.id = ?",
        [$notification_id]
    );

    if (!$notification_stmt) {
        error_log("Failed to query notification details for ID $notification_id");
        return false;
    }

    $notification = $notification_stmt->fetch();

    if (!$notification) {
        error_log("Notification not found for ID $notification_id");
        return false;
    }

    // إرسال عبر البريد الإلكتروني
    if ($settings['email_notifications']) {
        $email_sent = sendEmailNotification($notification);
        logNotificationSent($notification_id, 'email', $notification['email'], $email_sent);
    }

    // إرسال عبر الرسائل النصية (محاكاة)
    if ($settings['sms_notifications']) {
        $sms_sent = sendSMSNotification($notification);
        logNotificationSent($notification_id, 'sms', $notification['phone'], $sms_sent);
    }

    return true;
}

function sendEmailNotification($notification) {
    // محاكاة إرسال إيميل
    // في التطبيق الحقيقي، استخدم مكتبة مثل PHPMailer أو SwiftMailer

    $to = $notification['email'];
    $subject = $notification['title'] . ' - ' . SITE_NAME;
    $message = $notification['message'];

    if ($notification['action_url']) {
        $message .= "\n\nللمزيد من التفاصيل: " . SITE_URL . "/" . $notification['action_url'];
    }

    // return mail($to, $subject, $message);
    return true; // محاكاة نجح الإرسال
}

function sendSMSNotification($notification) {
    // محاكاة إرسال رسالة نصية
    // في التطبيق الحقيقي، استخدم خدمة SMS مثل Twilio أو Nexmo

    return true; // محاكاة نجح الإرسال
}

function logNotificationSent($notification_id, $method, $recipient, $success) {
    $db = getDB();

    $status = $success ? 'sent' : 'failed';

    $db->query(
        "INSERT INTO notification_log (notification_id, method, recipient, status)
         VALUES (?, ?, ?, ?)",
        [$notification_id, $method, $recipient, $status]
    );
}

function getUnreadNotificationsCount($user_id) {
    $db = getDB();

    $stmt = $db->query(
        "SELECT COUNT(*) as count FROM notifications
         WHERE user_id = ? AND is_read = FALSE",
        [$user_id]
    );

    if (!$stmt) {
        error_log("Failed to query unread notifications count for user $user_id");
        return 0;
    }

    $result = $stmt->fetch();
    return $result ? $result['count'] : 0;
}

function markNotificationAsRead($notification_id, $user_id) {
    $db = getDB();

    return $db->query(
        "UPDATE notifications SET is_read = TRUE, read_at = NOW()
         WHERE id = ? AND user_id = ?",
        [$notification_id, $user_id]
    );
}

// دوال المحادثات
function createOrGetConversation($offer_id, $customer_id, $vendor_id) {
    $db = getDB();

    // البحث عن محادثة موجودة
    $conversation = $db->query(
        "SELECT id FROM conversations WHERE offer_id = ?",
        [$offer_id]
    )->fetch();

    if ($conversation) {
        return $conversation['id'];
    }

    // إنشاء محادثة جديدة
    $db->query(
        "INSERT INTO conversations (offer_id, customer_id, vendor_id)
         VALUES (?, ?, ?)",
        [$offer_id, $customer_id, $vendor_id]
    );

    return $db->getConnection()->lastInsertId();
}

function sendMessage($conversation_id, $sender_id, $sender_type, $content, $message_type = 'text') {
    $db = getDB();

    // إدراج الرسالة
    $db->query(
        "INSERT INTO messages (conversation_id, sender_id, sender_type, content, message_type)
         VALUES (?, ?, ?, ?, ?)",
        [$conversation_id, $sender_id, $sender_type, $content, $message_type]
    );

    $message_id = $db->getConnection()->lastInsertId();

    // تحديث المحادثة
    $unread_field = $sender_type === 'customer' ? 'vendor_unread_count' : 'customer_unread_count';

    $db->query(
        "UPDATE conversations SET
         last_message_id = ?,
         last_message_at = NOW(),
         {$unread_field} = {$unread_field} + 1
         WHERE id = ?",
        [$message_id, $conversation_id]
    );

    return $message_id;
}

function getUnreadMessagesCount($user_id, $user_type) {
    $db = getDB();

    $field = $user_type === 'customer' ? 'customer_unread_count' : 'vendor_unread_count';
    $user_field = $user_type === 'customer' ? 'customer_id' : 'vendor_id';

    $stmt = $db->query(
        "SELECT SUM({$field}) as count FROM conversations
         WHERE {$user_field} = ? AND status = 'active'",
        [$user_id]
    );

    if (!$stmt) {
        error_log("Failed to query unread messages count for user $user_id");
        return 0;
    }

    $result = $stmt->fetch();
    return $result ? ($result['count'] ?? 0) : 0;
}

// دالة مساعدة لتحديد المسار الصحيح
function getCorrectPath($page, $folder = null) {
    $current_path = $_SERVER['REQUEST_URI'];

    if ($folder) {
        // إذا كان هناك مجلد محدد
        if (strpos($current_path, "/{$folder}/") !== false) {
            return $page;
        } else {
            return "{$folder}/{$page}";
        }
    } else {
        // تحديد المجلد حسب نوع المستخدم
        $user_type = getUserType();

        if (strpos($current_path, '/customer/') !== false) {
            return $page;
        } elseif (strpos($current_path, '/vendor/') !== false) {
            return $page;
        } elseif (strpos($current_path, '/admin/') !== false) {
            return $page;
        } else {
            // في المجلد الجذر
            if ($user_type === 'customer') {
                return "customer/{$page}";
            } elseif ($user_type === 'vendor') {
                return "vendor/{$page}";
            } elseif ($user_type === 'admin') {
                return "admin/{$page}";
            } else {
                return $page;
            }
        }
    }
}
?>
