<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: #2c5aa0 !important;
        }
        
        .btn-primary {
            background-color: #2c5aa0;
            border-color: #2c5aa0;
        }
        
        .btn-primary:hover {
            background-color: #1e3f73;
            border-color: #1e3f73;
        }
        
        .text-primary {
            color: #2c5aa0 !important;
        }
        
        .bg-primary {
            background-color: #2c5aa0 !important;
        }
        
        .alert {
            border-radius: 10px;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .navbar {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-car me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="request-part.php">طلب قطعة غيار</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">اتصل بنا</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <!-- التنبيهات -->
                        <li class="nav-item dropdown">
                            <?php
                            $unread_notifications = getUnreadNotificationsCount($_SESSION['user_id']);
                            ?>
                            <a class="nav-link position-relative" href="notifications.php">
                                <i class="fas fa-bell"></i>
                                <?php if ($unread_notifications > 0): ?>
                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                        <?php echo $unread_notifications > 99 ? '99+' : $unread_notifications; ?>
                                    </span>
                                <?php endif; ?>
                            </a>
                        </li>

                        <!-- الرسائل -->
                        <li class="nav-item dropdown">
                            <?php
                            $unread_messages = getUnreadMessagesCount($_SESSION['user_id'], getUserType());
                            $messages_url = getUserType() === 'customer' ? 'customer/messages.php' : 'vendor/messages.php';
                            ?>
                            <a class="nav-link position-relative" href="<?php echo $messages_url; ?>">
                                <i class="fas fa-comments"></i>
                                <?php if ($unread_messages > 0): ?>
                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-primary">
                                        <?php echo $unread_messages > 99 ? '99+' : $unread_messages; ?>
                                    </span>
                                <?php endif; ?>
                            </a>
                        </li>

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo $_SESSION['user_name']; ?>
                            </a>
                            <ul class="dropdown-menu">
                                <?php if (getUserType() == 'customer'): ?>
                                    <li><a class="dropdown-item" href="customer/dashboard.php">
                                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                    </a></li>
                                    <li><a class="dropdown-item" href="customer/my-requests.php">
                                        <i class="fas fa-list me-2"></i>طلباتي
                                    </a></li>
                                    <li><a class="dropdown-item" href="customer/messages.php">
                                        <i class="fas fa-comments me-2"></i>الرسائل
                                        <?php if ($unread_messages > 0): ?>
                                            <span class="badge bg-primary ms-1"><?php echo $unread_messages; ?></span>
                                        <?php endif; ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="customer/commissions.php">
                                        <i class="fas fa-percentage me-2"></i>عمولات الخدمة
                                    </a></li>
                                <?php elseif (getUserType() == 'vendor'): ?>
                                    <li><a class="dropdown-item" href="vendor/dashboard.php">
                                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                    </a></li>
                                    <li><a class="dropdown-item" href="vendor/my-offers.php">
                                        <i class="fas fa-handshake me-2"></i>عروضي
                                    </a></li>
                                    <li><a class="dropdown-item" href="vendor/messages.php">
                                        <i class="fas fa-comments me-2"></i>الرسائل
                                        <?php if ($unread_messages > 0): ?>
                                            <span class="badge bg-primary ms-1"><?php echo $unread_messages; ?></span>
                                        <?php endif; ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="vendor/commissions.php">
                                        <i class="fas fa-percentage me-2"></i>العمولات
                                    </a></li>
                                <?php elseif (getUserType() == 'admin'): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">
                                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                    </a></li>
                                    <li><a class="dropdown-item" href="admin/users.php">
                                        <i class="fas fa-users me-2"></i>إدارة المستخدمين
                                    </a></li>
                                    <li><a class="dropdown-item" href="admin/commissions.php">
                                        <i class="fas fa-percentage me-2"></i>إدارة العمولات
                                    </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="notifications.php">
                                    <i class="fas fa-bell me-2"></i>التنبيهات
                                    <?php if ($unread_notifications > 0): ?>
                                        <span class="badge bg-danger ms-1"><?php echo $unread_notifications; ?></span>
                                    <?php endif; ?>
                                </a></li>
                                <li><a class="dropdown-item" href="profile.php">
                                    <i class="fas fa-user me-2"></i>الملف الشخصي
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">تسجيل الدخول</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary btn-sm ms-2" href="register.php">إنشاء حساب</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Alert Messages -->
    <?php 
    $alert = getAlert();
    if ($alert): 
    ?>
        <div class="container mt-3">
            <div class="alert alert-<?php echo $alert['type']; ?> alert-dismissible fade show" role="alert">
                <?php echo $alert['message']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="container mt-4">
