<?php
/**
 * ملف تثبيت موقع صناعية
 * Installation file for Sanaeya website
 */

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'sanaeya_db';

$success = false;
$errors = [];

if ($_POST && isset($_POST['install'])) {
    try {
        // الاتصال بـ MySQL بدون تحديد قاعدة البيانات
        $pdo = new PDO("mysql:host=$db_host;charset=utf8mb4", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // قراءة ملف SQL
        $sql_file = 'database/create_database.sql';
        if (!file_exists($sql_file)) {
            throw new Exception('ملف قاعدة البيانات غير موجود');
        }
        
        $sql_content = file_get_contents($sql_file);
        
        // تنفيذ الاستعلامات
        $statements = explode(';', $sql_content);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        // إنشاء مستخدمين تجريبيين
        $pdo->exec("USE $db_name");
        
        // عميل تجريبي
        $customer_password = password_hash('123456', PASSWORD_DEFAULT);
        $pdo->prepare("INSERT INTO users (name, email, phone, password, user_type, status, email_verified) VALUES (?, ?, ?, ?, ?, ?, ?)")
            ->execute(['عميل تجريبي', '<EMAIL>', '0501234567', $customer_password, 'customer', 'active', 1]);
        
        // محل تجريبي
        $vendor_password = password_hash('123456', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (name, email, phone, password, user_type, status, email_verified) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute(['محل تجريبي', '<EMAIL>', '0507654321', $vendor_password, 'vendor', 'active', 1]);
        $vendor_id = $pdo->lastInsertId();
        
        // ملف تعريف المحل التجريبي
        $pdo->prepare("INSERT INTO vendor_profiles (user_id, business_name, address, city, description) VALUES (?, ?, ?, ?, ?)")
            ->execute([$vendor_id, 'محل قطع غيار الرياض', 'شارع الملك فهد، الرياض', 'الرياض', 'محل متخصص في قطع غيار جميع أنواع السيارات']);
        
        // إنشاء مجلدات الرفع
        $upload_dirs = ['uploads/', 'uploads/requests/', 'uploads/offers/', 'uploads/profiles/'];
        foreach ($upload_dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
        
        $success = true;
        
    } catch (Exception $e) {
        $errors[] = 'خطأ في التثبيت: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت موقع صناعية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
            min-height: 100vh;
        }
        .install-card {
            max-width: 600px;
            margin: 50px auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card install-card">
            <div class="card-header bg-primary text-white text-center">
                <h2 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    تثبيت موقع صناعية
                </h2>
            </div>
            <div class="card-body p-5">
                <?php if ($success): ?>
                    <div class="alert alert-success text-center">
                        <i class="fas fa-check-circle fs-1 text-success mb-3"></i>
                        <h4>تم التثبيت بنجاح!</h4>
                        <p class="mb-4">تم إنشاء قاعدة البيانات وإعداد الموقع بنجاح.</p>
                        
                        <div class="row text-start">
                            <div class="col-md-6">
                                <h6>بيانات العميل التجريبي:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>الإيميل:</strong> <EMAIL></li>
                                    <li><strong>كلمة المرور:</strong> 123456</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>بيانات المحل التجريبي:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>الإيميل:</strong> <EMAIL></li>
                                    <li><strong>كلمة المرور:</strong> 123456</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <a href="index.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-home me-2"></i>
                                الذهاب للموقع
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <h6>حدثت الأخطاء التالية:</h6>
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <div class="text-center mb-4">
                        <i class="fas fa-database fs-1 text-primary mb-3"></i>
                        <h4>مرحباً بك في موقع صناعية</h4>
                        <p class="text-muted">سيتم إنشاء قاعدة البيانات وإعداد الموقع للاستخدام</p>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6>متطلبات التثبيت:</h6>
                        <ul class="mb-0">
                            <li>خادم ويب (Apache/Nginx)</li>
                            <li>PHP 7.4 أو أحدث</li>
                            <li>MySQL 5.7 أو أحدث</li>
                            <li>امتداد PDO لـ PHP</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6>إعدادات قاعدة البيانات الحالية:</h6>
                        <ul class="mb-0">
                            <li><strong>الخادم:</strong> <?php echo $db_host; ?></li>
                            <li><strong>المستخدم:</strong> <?php echo $db_user; ?></li>
                            <li><strong>قاعدة البيانات:</strong> <?php echo $db_name; ?></li>
                        </ul>
                        <small class="text-muted">يمكنك تعديل هذه الإعدادات في ملف config/database.php</small>
                    </div>
                    
                    <form method="POST">
                        <div class="text-center">
                            <button type="submit" name="install" class="btn btn-primary btn-lg px-5">
                                <i class="fas fa-play me-2"></i>
                                بدء التثبيت
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>
