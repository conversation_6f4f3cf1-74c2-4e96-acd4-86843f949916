<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// إعادة توجيه المستخدمين المسجلين
if (isLoggedIn()) {
    redirect('index.php');
}

$errors = [];

if ($_POST) {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($email) || empty($password)) {
        $errors[] = 'البريد الإلكتروني وكلمة المرور مطلوبان';
    } else {
        $db = getDB();
        $stmt = $db->query("SELECT * FROM users WHERE email = ? AND status = 'active'", [$email]);
        $user = $stmt->fetch();
        
        if ($user && verifyPassword($password, $user['password'])) {
            // تسجيل الدخول بنجاح
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_type'] = $user['user_type'];
            
            // تذكرني
            if ($remember) {
                $token = generateToken();
                setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 يوم
                // يمكن حفظ التوكن في قاعدة البيانات للأمان
            }
            
            // إعادة التوجيه حسب نوع المستخدم
            $redirect_url = 'index.php';
            if ($user['user_type'] === 'customer') {
                $redirect_url = 'customer/dashboard.php';
            } elseif ($user['user_type'] === 'vendor') {
                $redirect_url = 'vendor/dashboard.php';
            } elseif ($user['user_type'] === 'admin') {
                $redirect_url = 'admin/dashboard.php';
            }
            
            // التحقق من وجود رابط إعادة التوجيه
            if (isset($_GET['redirect'])) {
                $redirect_url = sanitize($_GET['redirect']);
            }
            
            showAlert('مرحباً بك ' . $user['name'], 'success');
            redirect($redirect_url);
        } else {
            $errors[] = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
        }
    }
}

$page_title = 'تسجيل الدخول';
include 'includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-header bg-primary text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </h4>
            </div>
            <div class="card-body p-4">
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form method="POST">
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-envelope"></i>
                            </span>
                            <input type="email" name="email" class="form-control" value="<?php echo $_POST['email'] ?? ''; ?>" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">كلمة المرور</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" name="password" class="form-control" required>
                        </div>
                    </div>

                    <div class="mb-3 d-flex justify-content-between align-items-center">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                تذكرني
                            </label>
                        </div>
                        <a href="forgot-password.php" class="text-decoration-none">نسيت كلمة المرور؟</a>
                    </div>

                    <button type="submit" class="btn btn-primary w-100 mb-3">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول
                    </button>
                </form>

                <div class="text-center">
                    <p class="mb-0">ليس لديك حساب؟ <a href="register.php">إنشاء حساب جديد</a></p>
                </div>

                <hr class="my-4">

                <!-- روابط سريعة للتجربة -->
                <div class="text-center">
                    <p class="text-muted small mb-2">للتجربة السريعة:</p>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="fillDemo('customer')">
                            <i class="fas fa-user me-1"></i>
                            تجربة كعميل
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="fillDemo('vendor')">
                            <i class="fas fa-store me-1"></i>
                            تجربة كمحل
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function fillDemo(type) {
    if (type === 'customer') {
        document.querySelector('input[name="email"]').value = '<EMAIL>';
        document.querySelector('input[name="password"]').value = '123456';
    } else if (type === 'vendor') {
        document.querySelector('input[name="email"]').value = '<EMAIL>';
        document.querySelector('input[name="password"]').value = '123456';
    }
}
</script>

<?php include 'includes/footer.php'; ?>
