<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// إعادة توجيه المستخدمين المسجلين
if (isLoggedIn()) {
    redirect('index.php');
}

$errors = [];

if ($_POST) {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($email) || empty($password)) {
        $errors[] = 'البريد الإلكتروني وكلمة المرور مطلوبان';
    } else {
        $db = getDB();
        $stmt = $db->query("SELECT * FROM users WHERE email = ? AND status = 'active'", [$email]);
        $user = $stmt->fetch();
        
        if ($user && verifyPassword($password, $user['password'])) {
            // تسجيل الدخول بنجاح
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_type'] = $user['user_type'];
            
            // تذكرني
            if ($remember) {
                $token = generateToken();
                setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 يوم
                // يمكن حفظ التوكن في قاعدة البيانات للأمان
            }
            
            // إعادة التوجيه حسب نوع المستخدم
            $redirect_url = 'index.php';
            if ($user['user_type'] === 'customer') {
                $redirect_url = 'customer/dashboard.php';
            } elseif ($user['user_type'] === 'vendor') {
                $redirect_url = 'vendor/dashboard.php';
            } elseif ($user['user_type'] === 'admin') {
                $redirect_url = 'admin/dashboard.php';
            }
            
            // التحقق من وجود رابط إعادة التوجيه
            if (isset($_GET['redirect'])) {
                $redirect_url = sanitize($_GET['redirect']);
            }
            
            showAlert('مرحباً بك ' . $user['name'], 'success');
            redirect($redirect_url);
        } else {
            $errors[] = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
        }
    }
}

$page_title = 'تسجيل الدخول';
include 'includes/header.php';
?>

<div class="row justify-content-center min-vh-100 align-items-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow-lg border-0 animate__animated animate__fadeInUp">
            <div class="card-header text-center border-0" style="background: var(--gradient-primary);">
                <div class="py-3">
                    <i class="fas fa-user-circle display-4 text-white mb-3"></i>
                    <h3 class="text-white fw-bold mb-0">تسجيل الدخول</h3>
                    <p class="text-white-50 mb-0">مرحباً بعودتك إلى صناعية</p>
                </div>
            </div>
            <div class="card-body p-5">
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form method="POST" class="animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                    <div class="mb-4">
                        <label class="form-label fw-bold text-dark">البريد الإلكتروني</label>
                        <div class="input-group input-group-lg">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="fas fa-envelope text-primary"></i>
                            </span>
                            <input type="email" name="email" class="form-control border-start-0 ps-0"
                                   placeholder="أدخل بريدك الإلكتروني"
                                   value="<?php echo $_POST['email'] ?? ''; ?>" required>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="form-label fw-bold text-dark">كلمة المرور</label>
                        <div class="input-group input-group-lg">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="fas fa-lock text-primary"></i>
                            </span>
                            <input type="password" name="password" class="form-control border-start-0 ps-0"
                                   placeholder="أدخل كلمة المرور" required>
                            <button class="btn btn-outline-secondary border-start-0" type="button" onclick="togglePassword()">
                                <i class="fas fa-eye" id="toggleIcon"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-4 d-flex justify-content-between align-items-center">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label fw-500" for="remember">
                                تذكرني
                            </label>
                        </div>
                        <a href="forgot-password.php" class="text-decoration-none text-primary fw-500">نسيت كلمة المرور؟</a>
                    </div>

                    <button type="submit" class="btn btn-lg w-100 mb-4 shadow-lg" style="background: var(--gradient-primary); border: none;">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول
                    </button>
                </form>

                <div class="text-center">
                    <p class="mb-0">ليس لديك حساب؟ <a href="register.php">إنشاء حساب جديد</a></p>
                </div>

                <hr class="my-4">

                <!-- روابط سريعة للتجربة -->
                <div class="text-center">
                    <p class="text-muted small mb-2">للتجربة السريعة:</p>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="fillDemo('customer')">
                            <i class="fas fa-user me-1"></i>
                            تجربة كعميل
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="fillDemo('vendor')">
                            <i class="fas fa-store me-1"></i>
                            تجربة كمحل
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function fillDemo(type) {
    if (type === 'customer') {
        document.querySelector('input[name="email"]').value = '<EMAIL>';
        document.querySelector('input[name="password"]').value = '123456';
    } else if (type === 'vendor') {
        document.querySelector('input[name="email"]').value = '<EMAIL>';
        document.querySelector('input[name="password"]').value = '123456';
    }
}

function togglePassword() {
    const passwordInput = document.querySelector('input[name="password"]');
    const toggleIcon = document.getElementById('toggleIcon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// تأثيرات إضافية
document.addEventListener('DOMContentLoaded', function() {
    // تأثير التركيز على الحقول
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'translateY(-2px)';
            this.parentElement.style.boxShadow = '0 8px 25px rgba(0,0,0,0.1)';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'translateY(0)';
            this.parentElement.style.boxShadow = 'none';
        });
    });
});
</script>

<style>
.min-vh-100 {
    min-height: 100vh;
}

.input-group {
    transition: all 0.3s ease;
}

.form-control:focus {
    box-shadow: none;
    border-color: var(--primary-color);
}

.btn:hover {
    transform: translateY(-2px);
}

.card {
    backdrop-filter: blur(10px);
}

body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
}
</style>

<?php include 'includes/footer.php'; ?>
