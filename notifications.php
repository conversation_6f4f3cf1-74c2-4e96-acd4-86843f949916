<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    showAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'error');
    redirect('login.php');
}

$db = getDB();
$user_id = $_SESSION['user_id'];
$user_type = getUserType();

// معالجة تحديد التنبيه كمقروء
if (isset($_GET['mark_read']) && $_GET['mark_read']) {
    $notification_id = intval($_GET['mark_read']);
    $db->query(
        "UPDATE notifications SET is_read = TRUE, read_at = NOW() WHERE id = ? AND user_id = ?",
        [$notification_id, $user_id]
    );
    
    // إعادة توجيه إذا كان هناك رابط
    if (isset($_GET['redirect']) && $_GET['redirect']) {
        redirect($_GET['redirect']);
    }
}

// معالجة تحديد جميع التنبيهات كمقروءة
if (isset($_POST['mark_all_read'])) {
    $db->query(
        "UPDATE notifications SET is_read = TRUE, read_at = NOW() WHERE user_id = ? AND is_read = FALSE",
        [$user_id]
    );
    redirect('notifications.php');
}

// فلترة التنبيهات
$filter = $_GET['filter'] ?? 'all';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 15;
$offset = ($page - 1) * $per_page;

// بناء الاستعلام
$where_conditions = ["user_id = ?"];
$params = [$user_id];

if ($filter === 'unread') {
    $where_conditions[] = "is_read = FALSE";
} elseif ($filter === 'read') {
    $where_conditions[] = "is_read = TRUE";
} elseif ($filter !== 'all') {
    $where_conditions[] = "type = ?";
    $params[] = $filter;
}

$where_clause = implode(' AND ', $where_conditions);

// عدد التنبيهات الإجمالي
$count_query = "SELECT COUNT(*) as total FROM notifications WHERE $where_clause";
$total_notifications = $db->query($count_query, $params)->fetch()['total'];
$total_pages = ceil($total_notifications / $per_page);

// جلب التنبيهات
$notifications_query = "SELECT * FROM notifications WHERE $where_clause ORDER BY created_at DESC LIMIT $per_page OFFSET $offset";
$notifications = $db->query($notifications_query, $params)->fetchAll();

// إحصائيات التنبيهات
$stats = $db->query(
    "SELECT 
     COUNT(*) as total,
     COUNT(CASE WHEN is_read = FALSE THEN 1 END) as unread,
     COUNT(CASE WHEN is_read = TRUE THEN 1 END) as read,
     COUNT(CASE WHEN priority = 'high' OR priority = 'urgent' THEN 1 END) as important
     FROM notifications WHERE user_id = ?",
    [$user_id]
)->fetch();

$page_title = 'التنبيهات والإشعارات';
include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold text-primary">
        <i class="fas fa-bell me-2"></i>
        التنبيهات والإشعارات
        <?php if ($stats['unread'] > 0): ?>
            <span class="badge bg-danger"><?php echo $stats['unread']; ?></span>
        <?php endif; ?>
    </h2>
    <div class="d-flex gap-2">
        <?php if ($stats['unread'] > 0): ?>
            <form method="POST" class="d-inline">
                <button type="submit" name="mark_all_read" class="btn btn-outline-success">
                    <i class="fas fa-check-double me-1"></i>
                    تحديد الكل كمقروء
                </button>
            </form>
        <?php endif; ?>
        <a href="<?php echo $user_type; ?>/dashboard.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            لوحة التحكم
        </a>
    </div>
</div>

<!-- إحصائيات التنبيهات -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['total']; ?></h4>
                <p class="mb-0">إجمالي التنبيهات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['unread']; ?></h4>
                <p class="mb-0">غير مقروءة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['read']; ?></h4>
                <p class="mb-0">مقروءة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['important']; ?></h4>
                <p class="mb-0">مهمة</p>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر التنبيهات -->
<div class="card mb-4">
    <div class="card-body">
        <div class="d-flex gap-2 flex-wrap">
            <a href="?filter=all" class="btn btn-<?php echo $filter === 'all' ? 'primary' : 'outline-primary'; ?> btn-sm">
                جميع التنبيهات
            </a>
            <a href="?filter=unread" class="btn btn-<?php echo $filter === 'unread' ? 'warning' : 'outline-warning'; ?> btn-sm">
                غير مقروءة (<?php echo $stats['unread']; ?>)
            </a>
            <a href="?filter=read" class="btn btn-<?php echo $filter === 'read' ? 'success' : 'outline-success'; ?> btn-sm">
                مقروءة
            </a>
            <a href="?filter=new_offer" class="btn btn-<?php echo $filter === 'new_offer' ? 'info' : 'outline-info'; ?> btn-sm">
                عروض جديدة
            </a>
            <a href="?filter=new_message" class="btn btn-<?php echo $filter === 'new_message' ? 'secondary' : 'outline-secondary'; ?> btn-sm">
                رسائل جديدة
            </a>
            <a href="?filter=commission_due" class="btn btn-<?php echo $filter === 'commission_due' ? 'dark' : 'outline-dark'; ?> btn-sm">
                عمولات مستحقة
            </a>
        </div>
    </div>
</div>

<!-- قائمة التنبيهات -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">تنبيهاتي (<?php echo $total_notifications; ?>)</h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($notifications)): ?>
            <div class="text-center py-5">
                <i class="fas fa-bell-slash fs-1 text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد تنبيهات</h5>
                <p class="text-muted">
                    <?php if ($filter === 'unread'): ?>
                        جميع تنبيهاتك مقروءة
                    <?php else: ?>
                        لم تصلك أي تنبيهات بعد
                    <?php endif; ?>
                </p>
            </div>
        <?php else: ?>
            <div class="list-group list-group-flush">
                <?php foreach ($notifications as $notification): ?>
                    <div class="list-group-item <?php echo !$notification['is_read'] ? 'bg-light border-start border-primary border-3' : ''; ?>">
                        <div class="d-flex w-100 justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0 d-flex align-items-center">
                                        <i class="fas fa-<?php 
                                            echo $notification['type'] === 'new_offer' ? 'handshake text-success' : 
                                                ($notification['type'] === 'new_message' ? 'comment text-primary' : 
                                                ($notification['type'] === 'commission_due' ? 'percentage text-warning' : 
                                                ($notification['type'] === 'offer_accepted' ? 'check-circle text-success' : 
                                                ($notification['type'] === 'offer_rejected' ? 'times-circle text-danger' : 'bell text-info')))); 
                                        ?> me-2"></i>
                                        <?php echo htmlspecialchars($notification['title']); ?>
                                        
                                        <?php if (!$notification['is_read']): ?>
                                            <span class="badge bg-primary ms-2">جديد</span>
                                        <?php endif; ?>
                                        
                                        <?php if ($notification['priority'] === 'high' || $notification['priority'] === 'urgent'): ?>
                                            <span class="badge bg-danger ms-2">مهم</span>
                                        <?php endif; ?>
                                    </h6>
                                    <small class="text-muted">
                                        <?php echo formatArabicDate($notification['created_at']); ?>
                                    </small>
                                </div>
                                
                                <p class="mb-2 text-muted">
                                    <?php echo htmlspecialchars($notification['message']); ?>
                                </p>
                                
                                <div class="d-flex gap-2">
                                    <?php if ($notification['action_url']): ?>
                                        <a href="?mark_read=<?php echo $notification['id']; ?>&redirect=<?php echo urlencode($notification['action_url']); ?>" class="btn btn-primary btn-sm">
                                            <i class="fas fa-external-link-alt me-1"></i>
                                            عرض التفاصيل
                                        </a>
                                    <?php endif; ?>
                                    
                                    <?php if (!$notification['is_read']): ?>
                                        <a href="?mark_read=<?php echo $notification['id']; ?>" class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-check me-1"></i>
                                            تحديد كمقروء
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- ترقيم الصفحات -->
    <?php if ($total_pages > 1): ?>
        <div class="card-footer">
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&filter=<?php echo $filter; ?>">السابق</a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>&filter=<?php echo $filter; ?>"><?php echo $i; ?></a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&filter=<?php echo $filter; ?>">التالي</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    <?php endif; ?>
</div>

<!-- إعدادات التنبيهات -->
<div class="card mt-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-cog me-2"></i>
            إعدادات التنبيهات
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p class="mb-2"><strong>أنواع التنبيهات المفعلة:</strong></p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>عروض جديدة</li>
                    <li><i class="fas fa-check text-success me-2"></i>رسائل جديدة</li>
                    <li><i class="fas fa-check text-success me-2"></i>تحديثات العروض</li>
                    <li><i class="fas fa-check text-success me-2"></i>تذكيرات العمولات</li>
                </ul>
            </div>
            <div class="col-md-6">
                <p class="mb-2"><strong>طرق التنبيه:</strong></p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>تنبيهات داخل الموقع</li>
                    <li><i class="fas fa-check text-success me-2"></i>إشعارات بريد إلكتروني</li>
                    <li><i class="fas fa-times text-danger me-2"></i>رسائل نصية (غير مفعل)</li>
                </ul>
            </div>
        </div>
        <div class="text-center mt-3">
            <a href="notification-settings.php" class="btn btn-outline-primary">
                <i class="fas fa-cog me-2"></i>
                تخصيص إعدادات التنبيهات
            </a>
        </div>
    </div>
</div>

<script>
// تحديث تلقائي للتنبيهات كل دقيقة
setInterval(function() {
    fetch('ajax/check-notifications.php')
    .then(response => response.json())
    .then(data => {
        if (data.has_new_notifications) {
            // إظهار إشعار أو تحديث العداد
            const badge = document.querySelector('.badge.bg-danger');
            if (badge) {
                badge.textContent = data.unread_count;
            }
            
            // إظهار إشعار متصفح إذا كان مسموحاً
            if (Notification.permission === 'granted') {
                new Notification('تنبيه جديد', {
                    body: 'وصلك تنبيه جديد في موقع صناعية',
                    icon: '/assets/images/logo.png'
                });
            }
        }
    })
    .catch(error => console.error('Error checking notifications:', error));
}, 60000);

// طلب إذن الإشعارات
if (Notification.permission === 'default') {
    Notification.requestPermission();
}
</script>

<?php include 'includes/footer.php'; ?>
