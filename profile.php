<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    showAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'error');
    redirect('login.php');
}

$db = getDB();
$user_id = $_SESSION['user_id'];
$user_type = getUserType();

$errors = [];
$success = false;

// جلب بيانات المستخدم
$user = $db->query("SELECT * FROM users WHERE id = ?", [$user_id])->fetch();

// جلب بيانات المحل إذا كان المستخدم محل
$vendor_profile = null;
if ($user_type === 'vendor') {
    $vendor_profile = $db->query("SELECT * FROM vendor_profiles WHERE user_id = ?", [$user_id])->fetch();
}

if ($_POST) {
    $name = sanitize($_POST['name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $phone = sanitize($_POST['phone'] ?? '');
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // التحقق من البيانات الأساسية
    if (empty($name)) {
        $errors[] = 'الاسم مطلوب';
    }
    
    if (empty($email) || !isValidEmail($email)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }
    
    if (empty($phone) || !isValidSaudiPhone($phone)) {
        $errors[] = 'رقم الهاتف غير صحيح';
    }
    
    // التحقق من عدم تكرار البريد الإلكتروني أو الهاتف
    if (empty($errors)) {
        $stmt = $db->query("SELECT id FROM users WHERE (email = ? OR phone = ?) AND id != ?", [$email, $phone, $user_id]);
        if ($stmt->fetch()) {
            $errors[] = 'البريد الإلكتروني أو رقم الهاتف مستخدم من قبل مستخدم آخر';
        }
    }
    
    // التحقق من كلمة المرور الجديدة
    if (!empty($new_password)) {
        if (empty($current_password)) {
            $errors[] = 'يجب إدخال كلمة المرور الحالية لتغيير كلمة المرور';
        } elseif (!verifyPassword($current_password, $user['password'])) {
            $errors[] = 'كلمة المرور الحالية غير صحيحة';
        } elseif (strlen($new_password) < 6) {
            $errors[] = 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل';
        } elseif ($new_password !== $confirm_password) {
            $errors[] = 'كلمة المرور الجديدة وتأكيدها غير متطابقتين';
        }
    }
    
    // تحديث البيانات
    if (empty($errors)) {
        // تحديث البيانات الأساسية
        $update_params = [$name, $email, $phone, $user_id];
        $update_query = "UPDATE users SET name = ?, email = ?, phone = ? WHERE id = ?";
        
        // إضافة كلمة المرور إذا تم تغييرها
        if (!empty($new_password)) {
            $hashed_password = hashPassword($new_password);
            $update_query = "UPDATE users SET name = ?, email = ?, phone = ?, password = ? WHERE id = ?";
            $update_params = [$name, $email, $phone, $hashed_password, $user_id];
        }
        
        $stmt = $db->query($update_query, $update_params);
        
        if ($stmt) {
            // تحديث بيانات المحل إذا كان المستخدم محل
            if ($user_type === 'vendor') {
                $business_name = sanitize($_POST['business_name'] ?? '');
                $commercial_register = sanitize($_POST['commercial_register'] ?? '');
                $address = sanitize($_POST['address'] ?? '');
                $city = sanitize($_POST['city'] ?? '');
                $district = sanitize($_POST['district'] ?? '');
                $working_hours = sanitize($_POST['working_hours'] ?? '');
                $description = sanitize($_POST['description'] ?? '');
                
                if ($vendor_profile) {
                    // تحديث الملف الموجود
                    $db->query(
                        "UPDATE vendor_profiles SET business_name = ?, commercial_register = ?, address = ?, city = ?, district = ?, working_hours = ?, description = ? WHERE user_id = ?",
                        [$business_name, $commercial_register, $address, $city, $district, $working_hours, $description, $user_id]
                    );
                } else {
                    // إنشاء ملف جديد
                    $db->query(
                        "INSERT INTO vendor_profiles (user_id, business_name, commercial_register, address, city, district, working_hours, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                        [$user_id, $business_name, $commercial_register, $address, $city, $district, $working_hours, $description]
                    );
                }
            }
            
            // تحديث بيانات الجلسة
            $_SESSION['user_name'] = $name;
            $_SESSION['user_email'] = $email;
            
            $success = true;
            showAlert('تم تحديث الملف الشخصي بنجاح', 'success');
            
            // إعادة جلب البيانات المحدثة
            $user = $db->query("SELECT * FROM users WHERE id = ?", [$user_id])->fetch();
            if ($user_type === 'vendor') {
                $vendor_profile = $db->query("SELECT * FROM vendor_profiles WHERE user_id = ?", [$user_id])->fetch();
            }
        } else {
            $errors[] = 'حدث خطأ أثناء تحديث البيانات';
        }
    }
}

$page_title = 'الملف الشخصي';
include 'includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    الملف الشخصي
                </h4>
            </div>
            <div class="card-body p-4">
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form method="POST">
                    <!-- البيانات الأساسية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-user me-2"></i>
                                البيانات الأساسية
                            </h5>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم الكامل *</label>
                            <input type="text" name="name" class="form-control" value="<?php echo htmlspecialchars($user['name']); ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني *</label>
                            <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهاتف *</label>
                            <input type="tel" name="phone" class="form-control" value="<?php echo htmlspecialchars($user['phone']); ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع الحساب</label>
                            <input type="text" class="form-control" value="<?php echo $user_type === 'customer' ? 'عميل' : ($user_type === 'vendor' ? 'محل قطع غيار' : 'مشرف'); ?>" readonly>
                        </div>
                    </div>

                    <?php if ($user_type === 'vendor'): ?>
                        <!-- بيانات المحل -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-store me-2"></i>
                                    بيانات المحل
                                </h5>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم المحل *</label>
                                <input type="text" name="business_name" class="form-control" value="<?php echo htmlspecialchars($vendor_profile['business_name'] ?? ''); ?>" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">السجل التجاري</label>
                                <input type="text" name="commercial_register" class="form-control" value="<?php echo htmlspecialchars($vendor_profile['commercial_register'] ?? ''); ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المدينة</label>
                                <select name="city" class="form-select">
                                    <option value="">اختر المدينة</option>
                                    <option value="الرياض" <?php echo ($vendor_profile['city'] ?? '') === 'الرياض' ? 'selected' : ''; ?>>الرياض</option>
                                    <option value="جدة" <?php echo ($vendor_profile['city'] ?? '') === 'جدة' ? 'selected' : ''; ?>>جدة</option>
                                    <option value="الدمام" <?php echo ($vendor_profile['city'] ?? '') === 'الدمام' ? 'selected' : ''; ?>>الدمام</option>
                                    <option value="مكة" <?php echo ($vendor_profile['city'] ?? '') === 'مكة' ? 'selected' : ''; ?>>مكة</option>
                                    <option value="المدينة" <?php echo ($vendor_profile['city'] ?? '') === 'المدينة' ? 'selected' : ''; ?>>المدينة</option>
                                    <option value="الطائف" <?php echo ($vendor_profile['city'] ?? '') === 'الطائف' ? 'selected' : ''; ?>>الطائف</option>
                                    <option value="تبوك" <?php echo ($vendor_profile['city'] ?? '') === 'تبوك' ? 'selected' : ''; ?>>تبوك</option>
                                    <option value="أبها" <?php echo ($vendor_profile['city'] ?? '') === 'أبها' ? 'selected' : ''; ?>>أبها</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحي</label>
                                <input type="text" name="district" class="form-control" value="<?php echo htmlspecialchars($vendor_profile['district'] ?? ''); ?>">
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label class="form-label">العنوان</label>
                                <textarea name="address" class="form-control" rows="2"><?php echo htmlspecialchars($vendor_profile['address'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">ساعات العمل</label>
                                <input type="text" name="working_hours" class="form-control" value="<?php echo htmlspecialchars($vendor_profile['working_hours'] ?? ''); ?>" placeholder="مثال: 8:00 ص - 10:00 م">
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label class="form-label">وصف المحل</label>
                                <textarea name="description" class="form-control" rows="3" placeholder="اكتب وصفاً مختصراً عن محلك وخدماتك"><?php echo htmlspecialchars($vendor_profile['description'] ?? ''); ?></textarea>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- تغيير كلمة المرور -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-lock me-2"></i>
                                تغيير كلمة المرور
                            </h5>
                            <p class="text-muted small">اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور</p>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label class="form-label">كلمة المرور الحالية</label>
                            <input type="password" name="current_password" class="form-control">
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label class="form-label">كلمة المرور الجديدة</label>
                            <input type="password" name="new_password" class="form-control">
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label class="form-label">تأكيد كلمة المرور الجديدة</label>
                            <input type="password" name="confirm_password" class="form-control">
                        </div>
                    </div>

                    <!-- معلومات الحساب -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات الحساب
                            </h5>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ التسجيل</label>
                            <input type="text" class="form-control" value="<?php echo formatArabicDate($user['created_at']); ?>" readonly>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">حالة الحساب</label>
                            <input type="text" class="form-control" value="<?php 
                                $status_text = [
                                    'active' => 'نشط',
                                    'inactive' => 'غير نشط',
                                    'suspended' => 'معلق'
                                ];
                                echo $status_text[$user['status']] ?? $user['status'];
                            ?>" readonly>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تأكيد البريد الإلكتروني</label>
                            <input type="text" class="form-control" value="<?php echo $user['email_verified'] ? 'مؤكد' : 'غير مؤكد'; ?>" readonly>
                        </div>
                        
                        <?php if ($user_type === 'vendor' && $vendor_profile): ?>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">التقييم</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" value="<?php echo $vendor_profile['rating'] > 0 ? number_format($vendor_profile['rating'], 1) . ' من 5' : 'لا يوجد تقييم بعد'; ?>" readonly>
                                    <span class="input-group-text">
                                        <i class="fas fa-star text-warning"></i>
                                    </span>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
