<?php
// إصلاح سريع لقاعدة البيانات والروابط
require_once 'config/database.php';
require_once 'includes/functions.php';

session_start();

echo "<h1>إصلاح سريع للنظام</h1>";

try {
    $db = getDB();
    
    // التحقق من وجود الجداول المطلوبة
    $required_tables = [
        'conversations' => "CREATE TABLE IF NOT EXISTS conversations (
            id INT PRIMARY KEY AUTO_INCREMENT,
            offer_id INT NOT NULL,
            customer_id INT NOT NULL,
            vendor_id INT NOT NULL,
            status ENUM('active', 'closed') DEFAULT 'active',
            last_message_id INT NULL,
            last_message_at TIMESTAMP NULL,
            customer_unread_count INT DEFAULT 0,
            vendor_unread_count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            <PERSON>OREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
            FOREIG<PERSON> KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (vendor_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_customer (customer_id),
            INDEX idx_vendor (vendor_id),
            INDEX idx_offer (offer_id)
        )",
        
        'messages' => "CREATE TABLE IF NOT EXISTS messages (
            id INT PRIMARY KEY AUTO_INCREMENT,
            conversation_id INT NOT NULL,
            sender_id INT NOT NULL,
            sender_type ENUM('customer', 'vendor') NOT NULL,
            content TEXT NOT NULL,
            message_type ENUM('text', 'system', 'image') DEFAULT 'text',
            is_read BOOLEAN DEFAULT FALSE,
            read_at TIMESTAMP NULL,
            is_deleted BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
            FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_conversation (conversation_id),
            INDEX idx_sender (sender_id),
            INDEX idx_created (created_at)
        )",
        
        'notifications' => "CREATE TABLE IF NOT EXISTS notifications (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            type ENUM('new_offer', 'offer_accepted', 'offer_rejected', 'new_message', 'commission_due', 'commission_overdue', 'new_request', 'request_closed', 'system_announcement') NOT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            data JSON NULL,
            action_url VARCHAR(500) NULL,
            is_read BOOLEAN DEFAULT FALSE,
            read_at TIMESTAMP NULL,
            priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_unread (user_id, is_read, created_at),
            INDEX idx_type (type),
            INDEX idx_priority (priority)
        )"
    ];
    
    foreach ($required_tables as $table_name => $create_sql) {
        try {
            $db->getConnection()->exec($create_sql);
            echo "<p style='color: green;'>✅ جدول $table_name جاهز</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ جدول $table_name موجود مسبقاً</p>";
        }
    }
    
    // إنشاء إعدادات التنبيهات للمستخدمين الموجودين
    try {
        $db->getConnection()->exec("
            CREATE TABLE IF NOT EXISTS notification_settings (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                email_notifications BOOLEAN DEFAULT TRUE,
                sms_notifications BOOLEAN DEFAULT FALSE,
                push_notifications BOOLEAN DEFAULT TRUE,
                new_offers BOOLEAN DEFAULT TRUE,
                offer_updates BOOLEAN DEFAULT TRUE,
                new_messages BOOLEAN DEFAULT TRUE,
                commission_reminders BOOLEAN DEFAULT TRUE,
                system_announcements BOOLEAN DEFAULT TRUE,
                quiet_hours_start TIME NULL,
                quiet_hours_end TIME NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_settings (user_id)
            )
        ");
        
        // إضافة إعدادات للمستخدمين الموجودين
        $db->getConnection()->exec("
            INSERT IGNORE INTO notification_settings (user_id) 
            SELECT id FROM users
        ");
        
        echo "<p style='color: green;'>✅ إعدادات التنبيهات جاهزة</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ إعدادات التنبيهات موجودة مسبقاً</p>";
    }
    
    // اختبار الدوال المهمة
    echo "<h2>اختبار الدوال:</h2>";
    
    if (function_exists('getUnreadNotificationsCount')) {
        echo "<p style='color: green;'>✅ دالة getUnreadNotificationsCount موجودة</p>";
    } else {
        echo "<p style='color: red;'>❌ دالة getUnreadNotificationsCount مفقودة</p>";
    }
    
    if (function_exists('getUnreadMessagesCount')) {
        echo "<p style='color: green;'>✅ دالة getUnreadMessagesCount موجودة</p>";
    } else {
        echo "<p style='color: red;'>❌ دالة getUnreadMessagesCount مفقودة</p>";
    }
    
    if (function_exists('createNotification')) {
        echo "<p style='color: green;'>✅ دالة createNotification موجودة</p>";
    } else {
        echo "<p style='color: red;'>❌ دالة createNotification مفقودة</p>";
    }
    
    if (function_exists('getCorrectPath')) {
        echo "<p style='color: green;'>✅ دالة getCorrectPath موجودة</p>";
    } else {
        echo "<p style='color: red;'>❌ دالة getCorrectPath مفقودة</p>";
    }
    
    echo "<h2>اختبار الروابط:</h2>";
    
    $test_files = [
        'customer/messages.php',
        'vendor/messages.php',
        'notifications.php'
    ];
    
    foreach ($test_files as $file) {
        if (file_exists($file)) {
            echo "<p style='color: green;'>✅ ملف $file موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ ملف $file مفقود</p>";
        }
    }
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin: 0 0 10px 0;'>✅ تم الإصلاح بنجاح!</h3>";
    echo "<p style='color: #155724; margin: 0;'>يمكنك الآن استخدام النظام بشكل طبيعي.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ حدث خطأ:</h3>";
    echo "<p style='color: #721c24; margin: 0;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='index.php'>العودة للرئيسية</a> | <a href='customer/dashboard.php'>لوحة تحكم العميل</a> | <a href='vendor/dashboard.php'>لوحة تحكم المحل</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
p { margin: 10px 0; }
</style>
