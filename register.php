<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// إعادة توجيه المستخدمين المسجلين
if (isLoggedIn()) {
    redirect('index.php');
}

$user_type = $_GET['type'] ?? 'customer';
if (!in_array($user_type, ['customer', 'vendor'])) {
    $user_type = 'customer';
}

$errors = [];
$success = false;

if ($_POST) {
    $name = sanitize($_POST['name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $phone = sanitize($_POST['phone'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $user_type = sanitize($_POST['user_type'] ?? 'customer');
    
    // التحقق من البيانات
    if (empty($name)) {
        $errors[] = 'الاسم مطلوب';
    }
    
    if (empty($email) || !isValidEmail($email)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }
    
    if (empty($phone) || !isValidSaudiPhone($phone)) {
        $errors[] = 'رقم الهاتف غير صحيح (يجب أن يبدأ بـ 05)';
    }
    
    if (empty($password) || strlen($password) < 6) {
        $errors[] = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    
    if ($password !== $confirm_password) {
        $errors[] = 'كلمة المرور وتأكيدها غير متطابقتين';
    }
    
    // التحقق من عدم وجود المستخدم مسبقاً
    if (empty($errors)) {
        $db = getDB();
        $stmt = $db->query("SELECT id FROM users WHERE email = ? OR phone = ?", [$email, $phone]);
        if ($stmt->fetch()) {
            $errors[] = 'البريد الإلكتروني أو رقم الهاتف مستخدم مسبقاً';
        }
    }
    
    // إنشاء المستخدم
    if (empty($errors)) {
        $hashed_password = hashPassword($password);
        $verification_token = generateToken();
        
        $stmt = $db->query(
            "INSERT INTO users (name, email, phone, password, user_type, verification_token) VALUES (?, ?, ?, ?, ?, ?)",
            [$name, $email, $phone, $hashed_password, $user_type, $verification_token]
        );
        
        if ($stmt) {
            $user_id = $db->lastInsertId();
            
            // إنشاء ملف تعريف للمحل إذا كان النوع vendor
            if ($user_type === 'vendor') {
                $business_name = sanitize($_POST['business_name'] ?? '');
                $address = sanitize($_POST['address'] ?? '');
                $city = sanitize($_POST['city'] ?? '');
                
                if (!empty($business_name)) {
                    $db->query(
                        "INSERT INTO vendor_profiles (user_id, business_name, address, city) VALUES (?, ?, ?, ?)",
                        [$user_id, $business_name, $address, $city]
                    );
                }
            }
            
            // إرسال إيميل التحقق (محاكاة)
            $verification_link = SITE_URL . "/verify.php?token=" . $verification_token;
            // sendEmail($email, "تأكيد الحساب - " . SITE_NAME, "يرجى النقر على الرابط لتأكيد حسابك: " . $verification_link);
            
            $success = true;
            showAlert('تم إنشاء الحساب بنجاح! يرجى تسجيل الدخول.', 'success');
        } else {
            $errors[] = 'حدث خطأ أثناء إنشاء الحساب';
        }
    }
}

$page_title = 'إنشاء حساب جديد';
include 'includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header bg-primary text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    إنشاء حساب جديد
                </h4>
            </div>
            <div class="card-body p-4">
                <?php if ($success): ?>
                    <div class="alert alert-success text-center">
                        <i class="fas fa-check-circle fs-2 mb-3"></i>
                        <h5>تم إنشاء الحساب بنجاح!</h5>
                        <p>يمكنك الآن <a href="login.php">تسجيل الدخول</a> إلى حسابك.</p>
                    </div>
                <?php else: ?>
                    
                    <!-- نوع الحساب -->
                    <div class="text-center mb-4">
                        <div class="btn-group" role="group">
                            <a href="?type=customer" class="btn <?php echo $user_type === 'customer' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                <i class="fas fa-user me-2"></i>عميل
                            </a>
                            <a href="?type=vendor" class="btn <?php echo $user_type === 'vendor' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                <i class="fas fa-store me-2"></i>محل قطع غيار
                            </a>
                        </div>
                    </div>

                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="POST">
                        <input type="hidden" name="user_type" value="<?php echo $user_type; ?>">
                        
                        <div class="mb-3">
                            <label class="form-label">الاسم الكامل *</label>
                            <input type="text" name="name" class="form-control" value="<?php echo $_POST['name'] ?? ''; ?>" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني *</label>
                            <input type="email" name="email" class="form-control" value="<?php echo $_POST['email'] ?? ''; ?>" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف *</label>
                            <input type="tel" name="phone" class="form-control" placeholder="05xxxxxxxx" value="<?php echo $_POST['phone'] ?? ''; ?>" required>
                        </div>

                        <?php if ($user_type === 'vendor'): ?>
                            <div class="mb-3">
                                <label class="form-label">اسم المحل *</label>
                                <input type="text" name="business_name" class="form-control" value="<?php echo $_POST['business_name'] ?? ''; ?>" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">العنوان</label>
                                <textarea name="address" class="form-control" rows="2"><?php echo $_POST['address'] ?? ''; ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">المدينة</label>
                                <select name="city" class="form-select">
                                    <option value="">اختر المدينة</option>
                                    <option value="الرياض" <?php echo ($_POST['city'] ?? '') === 'الرياض' ? 'selected' : ''; ?>>الرياض</option>
                                    <option value="جدة" <?php echo ($_POST['city'] ?? '') === 'جدة' ? 'selected' : ''; ?>>جدة</option>
                                    <option value="الدمام" <?php echo ($_POST['city'] ?? '') === 'الدمام' ? 'selected' : ''; ?>>الدمام</option>
                                    <option value="مكة" <?php echo ($_POST['city'] ?? '') === 'مكة' ? 'selected' : ''; ?>>مكة</option>
                                    <option value="المدينة" <?php echo ($_POST['city'] ?? '') === 'المدينة' ? 'selected' : ''; ?>>المدينة</option>
                                </select>
                            </div>
                        <?php endif; ?>

                        <div class="mb-3">
                            <label class="form-label">كلمة المرور *</label>
                            <input type="password" name="password" class="form-control" required>
                            <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">تأكيد كلمة المرور *</label>
                            <input type="password" name="confirm_password" class="form-control" required>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" required>
                            <label class="form-check-label" for="terms">
                                أوافق على <a href="terms.php" target="_blank">شروط الاستخدام</a> و <a href="privacy.php" target="_blank">سياسة الخصوصية</a>
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-user-plus me-2"></i>
                            إنشاء الحساب
                        </button>
                    </form>

                    <div class="text-center">
                        <p class="mb-0">لديك حساب بالفعل؟ <a href="login.php">تسجيل الدخول</a></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
