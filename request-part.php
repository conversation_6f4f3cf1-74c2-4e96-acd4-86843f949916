<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    showAlert('يجب تسجيل الدخول أولاً لطلب قطعة غيار', 'warning');
    redirect('login.php?redirect=request-part.php');
}

// التحقق من نوع المستخدم
if (getUserType() !== 'customer') {
    showAlert('هذه الصفحة مخصصة للعملاء فقط', 'error');
    redirect('index.php');
}

$errors = [];
$success = false;

if ($_POST) {
    $car_make = sanitize($_POST['car_make'] ?? '');
    $car_model = sanitize($_POST['car_model'] ?? '');
    $car_year = sanitize($_POST['car_year'] ?? '');
    $part_name = sanitize($_POST['part_name'] ?? '');
    $part_number = sanitize($_POST['part_number'] ?? '');
    $description = sanitize($_POST['description'] ?? '');
    $condition_type = sanitize($_POST['condition_type'] ?? 'both');
    $urgency = sanitize($_POST['urgency'] ?? 'medium');
    $budget_min = floatval($_POST['budget_min'] ?? 0);
    $budget_max = floatval($_POST['budget_max'] ?? 0);
    $location = sanitize($_POST['location'] ?? '');
    
    // التحقق من البيانات
    if (empty($car_make)) {
        $errors[] = 'ماركة السيارة مطلوبة';
    }
    
    if (empty($car_model)) {
        $errors[] = 'موديل السيارة مطلوب';
    }
    
    if (empty($car_year) || $car_year < 1980 || $car_year > date('Y') + 1) {
        $errors[] = 'سنة السيارة غير صحيحة';
    }
    
    if (empty($part_name)) {
        $errors[] = 'اسم القطعة مطلوب';
    }
    
    if ($budget_min > 0 && $budget_max > 0 && $budget_min > $budget_max) {
        $errors[] = 'الحد الأدنى للميزانية لا يمكن أن يكون أكبر من الحد الأقصى';
    }
    
    // معالجة رفع الصور
    $uploaded_images = [];
    if (isset($_FILES['images']) && !empty($_FILES['images']['name'][0])) {
        if (!is_dir('uploads/requests/')) {
            mkdir('uploads/requests/', 0755, true);
        }
        
        for ($i = 0; $i < count($_FILES['images']['name']); $i++) {
            if ($_FILES['images']['error'][$i] === UPLOAD_ERR_OK) {
                $file = [
                    'name' => $_FILES['images']['name'][$i],
                    'tmp_name' => $_FILES['images']['tmp_name'][$i],
                    'size' => $_FILES['images']['size'][$i],
                    'error' => $_FILES['images']['error'][$i]
                ];
                
                $filename = uploadFile($file, 'uploads/requests/');
                if ($filename) {
                    $uploaded_images[] = $filename;
                }
            }
        }
    }
    
    // إنشاء الطلب
    if (empty($errors)) {
        $db = getDB();
        $expires_at = date('Y-m-d H:i:s', strtotime('+7 days')); // انتهاء الصلاحية بعد 7 أيام
        $images_json = !empty($uploaded_images) ? json_encode($uploaded_images) : null;
        
        $stmt = $db->query(
            "INSERT INTO part_requests (customer_id, car_make, car_model, car_year, part_name, part_number, description, condition_type, urgency, budget_min, budget_max, location, images, expires_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            [
                $_SESSION['user_id'],
                $car_make,
                $car_model,
                $car_year,
                $part_name,
                $part_number,
                $description,
                $condition_type,
                $urgency,
                $budget_min > 0 ? $budget_min : null,
                $budget_max > 0 ? $budget_max : null,
                $location,
                $images_json,
                $expires_at
            ]
        );
        
        if ($stmt) {
            $request_id = $db->lastInsertId();
            
            // إرسال إشعارات للمحلات (محاكاة)
            // يمكن إضافة نظام إشعارات حقيقي لاحقاً
            
            $success = true;
            showAlert('تم إرسال طلبك بنجاح! ستصلك العروض قريباً.', 'success');
            redirect('customer/my-requests.php');
        } else {
            $errors[] = 'حدث خطأ أثناء إرسال الطلب';
        }
    }
}

$page_title = 'طلب قطعة غيار';
include 'includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    طلب قطعة غيار
                </h4>
            </div>
            <div class="card-body p-4">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    املأ البيانات التالية وستصلك عروض أسعار من المحلات المختصة خلال دقائق
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form method="POST" enctype="multipart/form-data">
                    <!-- معلومات السيارة -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-car me-2"></i>
                                معلومات السيارة
                            </h5>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label class="form-label">ماركة السيارة *</label>
                            <select name="car_make" class="form-select" required>
                                <option value="">اختر الماركة</option>
                                <option value="تويوتا" <?php echo ($_POST['car_make'] ?? '') === 'تويوتا' ? 'selected' : ''; ?>>تويوتا</option>
                                <option value="هوندا" <?php echo ($_POST['car_make'] ?? '') === 'هوندا' ? 'selected' : ''; ?>>هوندا</option>
                                <option value="نيسان" <?php echo ($_POST['car_make'] ?? '') === 'نيسان' ? 'selected' : ''; ?>>نيسان</option>
                                <option value="هيونداي" <?php echo ($_POST['car_make'] ?? '') === 'هيونداي' ? 'selected' : ''; ?>>هيونداي</option>
                                <option value="كيا" <?php echo ($_POST['car_make'] ?? '') === 'كيا' ? 'selected' : ''; ?>>كيا</option>
                                <option value="فورد" <?php echo ($_POST['car_make'] ?? '') === 'فورد' ? 'selected' : ''; ?>>فورد</option>
                                <option value="شيفروليه" <?php echo ($_POST['car_make'] ?? '') === 'شيفروليه' ? 'selected' : ''; ?>>شيفروليه</option>
                                <option value="مرسيدس" <?php echo ($_POST['car_make'] ?? '') === 'مرسيدس' ? 'selected' : ''; ?>>مرسيدس</option>
                                <option value="بي إم دبليو" <?php echo ($_POST['car_make'] ?? '') === 'بي إم دبليو' ? 'selected' : ''; ?>>بي إم دبليو</option>
                                <option value="أخرى" <?php echo ($_POST['car_make'] ?? '') === 'أخرى' ? 'selected' : ''; ?>>أخرى</option>
                            </select>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label class="form-label">الموديل *</label>
                            <input type="text" name="car_model" class="form-control" value="<?php echo $_POST['car_model'] ?? ''; ?>" placeholder="مثال: كامري" required>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label class="form-label">السنة *</label>
                            <select name="car_year" class="form-select" required>
                                <option value="">اختر السنة</option>
                                <?php for ($year = date('Y') + 1; $year >= 1980; $year--): ?>
                                    <option value="<?php echo $year; ?>" <?php echo ($_POST['car_year'] ?? '') == $year ? 'selected' : ''; ?>>
                                        <?php echo $year; ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                    </div>

                    <!-- معلومات القطعة -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-cog me-2"></i>
                                معلومات القطعة المطلوبة
                            </h5>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم القطعة *</label>
                            <input type="text" name="part_name" class="form-control" value="<?php echo $_POST['part_name'] ?? ''; ?>" placeholder="مثال: فلتر هواء" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم القطعة (إن وجد)</label>
                            <input type="text" name="part_number" class="form-control" value="<?php echo $_POST['part_number'] ?? ''; ?>" placeholder="مثال: 17801-21050">
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label class="form-label">وصف تفصيلي للقطعة</label>
                            <textarea name="description" class="form-control" rows="3" placeholder="أضف أي تفاصيل إضافية تساعد في تحديد القطعة المطلوبة"><?php echo $_POST['description'] ?? ''; ?></textarea>
                        </div>
                    </div>

                    <!-- تفضيلات الطلب -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-sliders-h me-2"></i>
                                تفضيلات الطلب
                            </h5>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label class="form-label">حالة القطعة</label>
                            <select name="condition_type" class="form-select">
                                <option value="both" <?php echo ($_POST['condition_type'] ?? 'both') === 'both' ? 'selected' : ''; ?>>جديدة أو مستعملة</option>
                                <option value="new" <?php echo ($_POST['condition_type'] ?? '') === 'new' ? 'selected' : ''; ?>>جديدة فقط</option>
                                <option value="used" <?php echo ($_POST['condition_type'] ?? '') === 'used' ? 'selected' : ''; ?>>مستعملة فقط</option>
                            </select>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label class="form-label">مستوى الأولوية</label>
                            <select name="urgency" class="form-select">
                                <option value="low" <?php echo ($_POST['urgency'] ?? '') === 'low' ? 'selected' : ''; ?>>عادي</option>
                                <option value="medium" <?php echo ($_POST['urgency'] ?? 'medium') === 'medium' ? 'selected' : ''; ?>>متوسط</option>
                                <option value="high" <?php echo ($_POST['urgency'] ?? '') === 'high' ? 'selected' : ''; ?>>عاجل</option>
                            </select>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label class="form-label">الموقع</label>
                            <input type="text" name="location" class="form-control" value="<?php echo $_POST['location'] ?? ''; ?>" placeholder="المدينة أو المنطقة">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الحد الأدنى للميزانية (ريال)</label>
                            <input type="number" name="budget_min" class="form-control" value="<?php echo $_POST['budget_min'] ?? ''; ?>" min="0" step="0.01">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الحد الأقصى للميزانية (ريال)</label>
                            <input type="number" name="budget_max" class="form-control" value="<?php echo $_POST['budget_max'] ?? ''; ?>" min="0" step="0.01">
                        </div>
                    </div>

                    <!-- رفع الصور -->
                    <div class="mb-4">
                        <label class="form-label">صور القطعة (اختياري)</label>
                        <input type="file" name="images[]" class="form-control" multiple accept="image/*">
                        <div class="form-text">يمكنك رفع عدة صور لتوضيح القطعة المطلوبة (الحد الأقصى 5 ميجا لكل صورة)</div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-paper-plane me-2"></i>
                            إرسال الطلب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
