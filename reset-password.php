<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// إعادة توجيه المستخدمين المسجلين
if (isLoggedIn()) {
    redirect('index.php');
}

$token = sanitize($_GET['token'] ?? '');
$errors = [];
$success = false;
$valid_token = false;

if (empty($token)) {
    showAlert('رابط غير صحيح', 'error');
    redirect('forgot-password.php');
}

// التحقق من صحة الرمز
$db = getDB();
$stmt = $db->query(
    "SELECT id, name, email FROM users WHERE reset_token = ? AND reset_token_expires > NOW() AND status = 'active'",
    [$token]
);
$user = $stmt->fetch();

if (!$user) {
    showAlert('رابط إعادة تعيين كلمة المرور غير صحيح أو منتهي الصلاحية', 'error');
    redirect('forgot-password.php');
}

$valid_token = true;

if ($_POST) {
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // التحقق من البيانات
    if (empty($new_password) || strlen($new_password) < 6) {
        $errors[] = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    
    if ($new_password !== $confirm_password) {
        $errors[] = 'كلمة المرور وتأكيدها غير متطابقتين';
    }
    
    // تحديث كلمة المرور
    if (empty($errors)) {
        $hashed_password = hashPassword($new_password);
        
        $stmt = $db->query(
            "UPDATE users SET password = ?, reset_token = NULL, reset_token_expires = NULL WHERE id = ?",
            [$hashed_password, $user['id']]
        );
        
        if ($stmt) {
            $success = true;
            showAlert('تم تغيير كلمة المرور بنجاح! يمكنك الآن تسجيل الدخول.', 'success');
        } else {
            $errors[] = 'حدث خطأ أثناء تحديث كلمة المرور';
        }
    }
}

$page_title = 'إعادة تعيين كلمة المرور';
include 'includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-header bg-success text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-lock me-2"></i>
                    إعادة تعيين كلمة المرور
                </h4>
            </div>
            <div class="card-body p-4">
                <?php if ($success): ?>
                    <div class="alert alert-success text-center">
                        <i class="fas fa-check-circle fs-2 mb-3"></i>
                        <h5>تم تغيير كلمة المرور بنجاح!</h5>
                        <p class="mb-3">يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة.</p>
                        <a href="login.php" class="btn btn-success">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            تسجيل الدخول
                        </a>
                    </div>
                <?php elseif ($valid_token): ?>
                    <div class="text-center mb-4">
                        <i class="fas fa-key fs-1 text-success mb-3"></i>
                        <h5>مرحباً <?php echo htmlspecialchars($user['name']); ?></h5>
                        <p class="text-muted">أدخل كلمة المرور الجديدة</p>
                    </div>

                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور الجديدة</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" name="new_password" class="form-control" required>
                            </div>
                            <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">تأكيد كلمة المرور الجديدة</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" name="confirm_password" class="form-control" required>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-success w-100 mb-3">
                            <i class="fas fa-save me-2"></i>
                            حفظ كلمة المرور الجديدة
                        </button>
                    </form>

                    <div class="text-center">
                        <p class="mb-0">تذكرت كلمة المرور؟ <a href="login.php">تسجيل الدخول</a></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
