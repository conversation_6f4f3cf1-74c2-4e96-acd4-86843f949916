<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من أن المستخدم مشرف
if (!isLoggedIn() || getUserType() !== 'admin') {
    showAlert('يجب تسجيل الدخول كمشرف لتحديث قاعدة البيانات', 'error');
    redirect('login.php');
}

$errors = [];
$success_messages = [];

if ($_POST && isset($_POST['update_database'])) {
    try {
        $db = getDB();
        
        // قراءة ملف التحديث
        $update_sql = file_get_contents('database/update_notifications.sql');
        
        if (!$update_sql) {
            throw new Exception('لا يمكن قراءة ملف التحديث');
        }
        
        // تقسيم الاستعلامات
        $queries = array_filter(array_map('trim', explode(';', $update_sql)));
        
        $db->beginTransaction();
        
        foreach ($queries as $query) {
            // تجاهل التعليقات والأسطر الفارغة
            if (empty($query) || strpos($query, '--') === 0) {
                continue;
            }
            
            try {
                $db->exec($query);
                $success_messages[] = "تم تنفيذ الاستعلام بنجاح";
            } catch (PDOException $e) {
                // تجاهل أخطاء الجداول الموجودة مسبقاً
                if (strpos($e->getMessage(), 'already exists') === false && 
                    strpos($e->getMessage(), 'Duplicate entry') === false) {
                    throw $e;
                }
            }
        }
        
        $db->commit();
        $success_messages[] = "تم تحديث قاعدة البيانات بنجاح!";
        
    } catch (Exception $e) {
        if (isset($db)) {
            $db->rollback();
        }
        $errors[] = 'حدث خطأ أثناء تحديث قاعدة البيانات: ' . $e->getMessage();
    }
}

$page_title = 'تحديث قاعدة البيانات';
include 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-warning text-white text-center">
                    <h3 class="mb-0">
                        <i class="fas fa-database me-2"></i>
                        تحديث قاعدة البيانات
                    </h3>
                </div>
                <div class="card-body">
                    
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>أخطاء:</h6>
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success_messages)): ?>
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>نجح التحديث:</h6>
                            <ul class="mb-0">
                                <?php foreach ($success_messages as $message): ?>
                                    <li><?php echo htmlspecialchars($message); ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <div class="mt-3">
                                <a href="admin/dashboard.php" class="btn btn-success">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    العودة للوحة التحكم
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>حول هذا التحديث:</h6>
                            <p class="mb-2">هذا التحديث سيقوم بـ:</p>
                            <ul class="mb-0">
                                <li>تحديث جدول التنبيهات ليدعم الميزات الجديدة</li>
                                <li>إنشاء جداول المحادثات والرسائل</li>
                                <li>إنشاء جداول العمولات المحدثة</li>
                                <li>إضافة إعدادات التنبيهات للمستخدمين</li>
                                <li>إنشاء نسخة احتياطية من البيانات الموجودة</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه مهم:</h6>
                            <ul class="mb-0">
                                <li>تأكد من إنشاء نسخة احتياطية من قاعدة البيانات قبل التحديث</li>
                                <li>قد يستغرق التحديث بضع دقائق</li>
                                <li>لا تغلق المتصفح أثناء عملية التحديث</li>
                                <li>تأكد من أن لديك صلاحيات كافية على قاعدة البيانات</li>
                            </ul>
                        </div>
                        
                        <!-- معلومات قاعدة البيانات الحالية -->
                        <div class="card bg-light mb-4">
                            <div class="card-body">
                                <h6 class="card-title">معلومات قاعدة البيانات الحالية:</h6>
                                <?php
                                try {
                                    $db = getDB();
                                    
                                    // فحص الجداول الموجودة
                                    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
                                    
                                    echo "<p><strong>الجداول الموجودة:</strong> " . count($tables) . "</p>";
                                    echo "<div class='row'>";
                                    
                                    $chunks = array_chunk($tables, ceil(count($tables) / 3));
                                    foreach ($chunks as $chunk) {
                                        echo "<div class='col-md-4'><ul class='list-unstyled small'>";
                                        foreach ($chunk as $table) {
                                            $icon = 'table';
                                            if (strpos($table, 'user') !== false) $icon = 'users';
                                            elseif (strpos($table, 'notification') !== false) $icon = 'bell';
                                            elseif (strpos($table, 'message') !== false) $icon = 'comments';
                                            elseif (strpos($table, 'commission') !== false) $icon = 'percentage';
                                            
                                            echo "<li><i class='fas fa-{$icon} me-1'></i>{$table}</li>";
                                        }
                                        echo "</ul></div>";
                                    }
                                    echo "</div>";
                                    
                                } catch (Exception $e) {
                                    echo "<p class='text-danger'>خطأ في الاتصال بقاعدة البيانات: " . htmlspecialchars($e->getMessage()) . "</p>";
                                }
                                ?>
                            </div>
                        </div>
                        
                        <form method="POST" onsubmit="return confirmUpdate()">
                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" id="backup_confirm" required>
                                <label class="form-check-label" for="backup_confirm">
                                    أؤكد أنني قمت بإنشاء نسخة احتياطية من قاعدة البيانات
                                </label>
                            </div>
                            
                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" id="understand_risks" required>
                                <label class="form-check-label" for="understand_risks">
                                    أفهم المخاطر وأريد المتابعة
                                </label>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" name="update_database" class="btn btn-warning btn-lg px-5">
                                    <i class="fas fa-database me-2"></i>
                                    بدء تحديث قاعدة البيانات
                                </button>
                            </div>
                        </form>
                        
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- معلومات إضافية -->
            <div class="card mt-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        أسئلة شائعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    ماذا لو فشل التحديث؟
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    إذا فشل التحديث، يمكنك استعادة النسخة الاحتياطية من قاعدة البيانات. 
                                    تأكد من الاحتفاظ بنسخة احتياطية قبل بدء التحديث.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    هل ستفقد البيانات الموجودة؟
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    لا، التحديث مصمم للحفاظ على جميع البيانات الموجودة. 
                                    سيتم إنشاء نسخة احتياطية من الجداول المتأثرة تلقائياً.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    كم يستغرق التحديث؟
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    عادة يستغرق التحديث من 30 ثانية إلى دقيقتين، 
                                    حسب حجم البيانات الموجودة وسرعة الخادم.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmUpdate() {
    return confirm('هل أنت متأكد من تحديث قاعدة البيانات؟ تأكد من إنشاء نسخة احتياطية أولاً.');
}

// إظهار شريط التقدم أثناء التحديث
document.querySelector('form').addEventListener('submit', function() {
    const button = this.querySelector('button[type="submit"]');
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
    button.disabled = true;
});
</script>

<?php include 'includes/footer.php'; ?>
