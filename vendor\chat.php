<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'vendor') {
    showAlert('يجب تسجيل الدخول كمحل للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();
$vendor_id = $_SESSION['user_id'];
$conversation_id = intval($_GET['conversation_id'] ?? 0);

if (!$conversation_id) {
    showAlert('محادثة غير صحيحة', 'error');
    redirect('messages.php');
}

// جلب تفاصيل المحادثة
$conversation = $db->query(
    "SELECT c.*, 
     o.price as offer_price, o.status as offer_status, o.condition_type, o.warranty_period, o.delivery_cost, o.delivery_time, o.notes as offer_notes,
     pr.part_name, pr.car_make, pr.car_model, pr.car_year, pr.description as request_description, pr.budget_min, pr.budget_max,
     cu.name as customer_name, cu.phone as customer_phone, cu.email as customer_email
     FROM conversations c
     JOIN offers o ON c.offer_id = o.id
     JOIN part_requests pr ON o.request_id = pr.id
     JOIN users cu ON c.customer_id = cu.id
     WHERE c.id = ? AND c.vendor_id = ?",
    [$conversation_id, $vendor_id]
)->fetch();

if (!$conversation) {
    showAlert('المحادثة غير موجودة أو ليس لديك صلاحية للوصول إليها', 'error');
    redirect('messages.php');
}

// معالجة إرسال رسالة جديدة
if ($_POST && isset($_POST['message'])) {
    $message_content = trim($_POST['message']);
    
    if (!empty($message_content)) {
        // إدراج الرسالة
        $message_id = $db->query(
            "INSERT INTO messages (conversation_id, sender_id, sender_type, content) VALUES (?, ?, 'vendor', ?)",
            [$conversation_id, $vendor_id, $message_content]
        );
        
        // تحديث المحادثة
        $db->query(
            "UPDATE conversations SET 
             last_message_id = ?, 
             last_message_at = NOW(), 
             customer_unread_count = customer_unread_count + 1 
             WHERE id = ?",
            [$message_id, $conversation_id]
        );
        
        // إنشاء تنبيه للعميل
        createNotification(
            $conversation['customer_id'],
            'new_message',
            'رسالة جديدة من محل',
            "وصلتك رسالة جديدة من {$_SESSION['user_name']} بخصوص {$conversation['part_name']}",
            ['conversation_id' => $conversation_id],
            "customer/chat.php?conversation_id={$conversation_id}"
        );
        
        redirect("chat.php?conversation_id={$conversation_id}");
    }
}

// جلب الرسائل
$messages = $db->query(
    "SELECT m.*, u.name as sender_name 
     FROM messages m
     JOIN users u ON m.sender_id = u.id
     WHERE m.conversation_id = ? AND m.is_deleted = FALSE
     ORDER BY m.created_at ASC",
    [$conversation_id]
)->fetchAll();

// تحديد الرسائل كمقروءة
$db->query(
    "UPDATE messages SET is_read = TRUE, read_at = NOW() 
     WHERE conversation_id = ? AND sender_type = 'customer' AND is_read = FALSE",
    [$conversation_id]
);

// تحديث عداد الرسائل غير المقروءة
$db->query(
    "UPDATE conversations SET vendor_unread_count = 0 WHERE id = ?",
    [$conversation_id]
);

$page_title = 'محادثة مع ' . $conversation['customer_name'];
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold text-primary">
        <i class="fas fa-comments me-2"></i>
        محادثة مع <?php echo htmlspecialchars($conversation['customer_name']); ?>
    </h2>
    <div class="d-flex gap-2">
        <a href="messages.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للرسائل
        </a>
    </div>
</div>

<div class="row">
    <!-- منطقة المحادثة -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            <?php echo htmlspecialchars($conversation['customer_name']); ?>
                        </h6>
                        <small>
                            <i class="fas fa-phone me-1"></i>
                            <?php echo htmlspecialchars($conversation['customer_phone']); ?>
                        </small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-light text-dark">
                            <?php echo count($messages); ?> رسالة
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- منطقة الرسائل -->
            <div class="card-body p-0" style="height: 400px; overflow-y: auto;" id="messages-container">
                <?php if (empty($messages)): ?>
                    <div class="text-center py-5 text-muted">
                        <i class="fas fa-comment-dots fs-1 mb-3"></i>
                        <p>لا توجد رسائل بعد. ابدأ المحادثة!</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($messages as $message): ?>
                        <div class="p-3 border-bottom">
                            <div class="d-flex <?php echo $message['sender_type'] === 'vendor' ? 'justify-content-end' : 'justify-content-start'; ?>">
                                <div class="<?php echo $message['sender_type'] === 'vendor' ? 'bg-primary text-white' : 'bg-light'; ?> rounded p-3" style="max-width: 70%;">
                                    <div class="mb-2">
                                        <?php echo nl2br(htmlspecialchars($message['content'])); ?>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="<?php echo $message['sender_type'] === 'vendor' ? 'text-white-50' : 'text-muted'; ?>">
                                            <?php echo $message['sender_type'] === 'vendor' ? 'أنت' : htmlspecialchars($message['sender_name']); ?>
                                        </small>
                                        <small class="<?php echo $message['sender_type'] === 'vendor' ? 'text-white-50' : 'text-muted'; ?>">
                                            <?php echo formatArabicDate($message['created_at']); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            
            <!-- نموذج إرسال رسالة -->
            <div class="card-footer">
                <form method="POST" class="d-flex gap-2">
                    <input type="text" name="message" class="form-control" placeholder="اكتب رسالتك هنا..." required>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- تفاصيل الطلب والعرض -->
    <div class="col-lg-4">
        <!-- تفاصيل الطلب -->
        <div class="card mb-3">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    تفاصيل الطلب
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">القطعة المطلوبة:</label>
                    <p class="mb-0"><?php echo htmlspecialchars($conversation['part_name']); ?></p>
                    <small class="text-muted">
                        <?php echo htmlspecialchars($conversation['car_make'] . ' ' . $conversation['car_model'] . ' ' . $conversation['car_year']); ?>
                    </small>
                </div>
                
                <?php if ($conversation['request_description']): ?>
                    <div class="mb-3">
                        <label class="form-label fw-bold">وصف العميل:</label>
                        <p class="mb-0"><?php echo nl2br(htmlspecialchars($conversation['request_description'])); ?></p>
                    </div>
                <?php endif; ?>
                
                <?php if ($conversation['budget_min'] || $conversation['budget_max']): ?>
                    <div class="mb-3">
                        <label class="form-label fw-bold">ميزانية العميل:</label>
                        <p class="mb-0">
                            <?php if ($conversation['budget_min'] && $conversation['budget_max']): ?>
                                <?php echo formatPrice($conversation['budget_min']) . ' - ' . formatPrice($conversation['budget_max']); ?>
                            <?php elseif ($conversation['budget_max']): ?>
                                حتى <?php echo formatPrice($conversation['budget_max']); ?>
                            <?php elseif ($conversation['budget_min']): ?>
                                من <?php echo formatPrice($conversation['budget_min']); ?>
                            <?php endif; ?>
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- تفاصيل العرض -->
        <div class="card mb-3">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-handshake me-2"></i>
                    عرضي
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">سعر العرض:</label>
                    <p class="mb-0 fs-4 text-success fw-bold"><?php echo formatPrice($conversation['offer_price']); ?></p>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">حالة القطعة:</label>
                    <p class="mb-0">
                        <span class="badge bg-<?php echo $conversation['condition_type'] === 'new' ? 'success' : 'warning'; ?>">
                            <?php echo $conversation['condition_type'] === 'new' ? 'جديدة' : 'مستعملة'; ?>
                        </span>
                    </p>
                </div>
                
                <?php if ($conversation['warranty_period'] > 0): ?>
                    <div class="mb-3">
                        <label class="form-label fw-bold">فترة الضمان:</label>
                        <p class="mb-0"><?php echo $conversation['warranty_period']; ?> شهر</p>
                    </div>
                <?php endif; ?>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">تكلفة التوصيل:</label>
                    <p class="mb-0">
                        <?php if ($conversation['delivery_cost'] > 0): ?>
                            <?php echo formatPrice($conversation['delivery_cost']); ?>
                        <?php else: ?>
                            مجاني
                        <?php endif; ?>
                    </p>
                </div>
                
                <?php if ($conversation['delivery_time']): ?>
                    <div class="mb-3">
                        <label class="form-label fw-bold">وقت التوصيل:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($conversation['delivery_time']); ?></p>
                    </div>
                <?php endif; ?>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">حالة العرض:</label>
                    <p class="mb-0">
                        <span class="badge bg-<?php 
                            echo $conversation['offer_status'] === 'pending' ? 'warning' : 
                                ($conversation['offer_status'] === 'accepted' ? 'success' : 'secondary'); 
                        ?> fs-6">
                            <?php 
                            $status_text = [
                                'pending' => 'في الانتظار',
                                'accepted' => 'مقبول',
                                'rejected' => 'مرفوض',
                                'expired' => 'منتهي الصلاحية'
                            ];
                            echo $status_text[$conversation['offer_status']] ?? $conversation['offer_status'];
                            ?>
                        </span>
                    </p>
                </div>
                
                <?php if ($conversation['offer_notes']): ?>
                    <div class="mb-3">
                        <label class="form-label fw-bold">ملاحظاتي:</label>
                        <p class="mb-0"><?php echo nl2br(htmlspecialchars($conversation['offer_notes'])); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- معلومات العميل -->
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h6 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات العميل
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 60px; height: 60px;">
                        <i class="fas fa-user fs-3"></i>
                    </div>
                    <h6 class="fw-bold"><?php echo htmlspecialchars($conversation['customer_name']); ?></h6>
                </div>
                
                <div class="d-grid gap-2">
                    <a href="tel:<?php echo htmlspecialchars($conversation['customer_phone']); ?>" class="btn btn-success">
                        <i class="fas fa-phone me-2"></i>
                        <?php echo htmlspecialchars($conversation['customer_phone']); ?>
                    </a>
                    
                    <a href="mailto:<?php echo htmlspecialchars($conversation['customer_email']); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-envelope me-2"></i>
                        إرسال إيميل
                    </a>
                </div>
                
                <?php if ($conversation['offer_status'] === 'accepted'): ?>
                    <div class="alert alert-success text-center mt-3">
                        <i class="fas fa-check-circle fs-2 mb-2"></i>
                        <h6>تهانينا!</h6>
                        <p class="small mb-0">العميل قبل عرضك. يرجى التواصل معه لإتمام الصفقة.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// التمرير التلقائي لأسفل منطقة الرسائل
function scrollToBottom() {
    const container = document.getElementById('messages-container');
    container.scrollTop = container.scrollHeight;
}

// تمرير لأسفل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    scrollToBottom();
});

// تحديث الرسائل كل 10 ثواني
setInterval(function() {
    fetch(`ajax/get-messages.php?conversation_id=<?php echo $conversation_id; ?>`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.messages.length > <?php echo count($messages); ?>) {
            location.reload();
        }
    })
    .catch(error => console.error('Error:', error));
}, 10000);

// إرسال الرسالة بالضغط على Enter
document.querySelector('input[name="message"]').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        e.preventDefault();
        this.closest('form').submit();
    }
});
</script>

<?php include '../includes/footer.php'; ?>
