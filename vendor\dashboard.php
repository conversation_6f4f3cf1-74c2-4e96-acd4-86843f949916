<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'vendor') {
    showAlert('يجب تسجيل الدخول كمحل للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();
$vendor_id = $_SESSION['user_id'];

// إحصائيات المحل
$stats = [];

// إجمالي العروض المقدمة
$stmt = $db->query("SELECT COUNT(*) as total FROM offers WHERE vendor_id = ?", [$vendor_id]);
$stats['total_offers'] = $stmt->fetch()['total'];

// العروض المقبولة
$stmt = $db->query("SELECT COUNT(*) as accepted FROM offers WHERE vendor_id = ? AND status = 'accepted'", [$vendor_id]);
$stats['accepted_offers'] = $stmt->fetch()['accepted'];

// العروض في الانتظار
$stmt = $db->query("SELECT COUNT(*) as pending FROM offers WHERE vendor_id = ? AND status = 'pending'", [$vendor_id]);
$stats['pending_offers'] = $stmt->fetch()['pending'];

// إجمالي المبيعات
$stmt = $db->query("SELECT COUNT(*) as total, COALESCE(SUM(amount), 0) as total_amount FROM transactions WHERE vendor_id = ? AND payment_status = 'completed'", [$vendor_id]);
$sales_data = $stmt->fetch();
$stats['total_sales'] = $sales_data['total'];
$stats['total_revenue'] = $sales_data['total_amount'];

// الطلبات الجديدة المتاحة (لم يتم تقديم عرض عليها من هذا المحل)
$stmt = $db->query(
    "SELECT COUNT(*) as new_requests FROM part_requests pr 
     WHERE pr.status IN ('pending', 'active') 
     AND pr.id NOT IN (SELECT request_id FROM offers WHERE vendor_id = ?)",
    [$vendor_id]
);
$stats['new_requests'] = $stmt->fetch()['new_requests'];

// أحدث الطلبات المتاحة
$new_requests = $db->query(
    "SELECT pr.*, u.name as customer_name FROM part_requests pr 
     JOIN users u ON pr.customer_id = u.id 
     WHERE pr.status IN ('pending', 'active') 
     AND pr.id NOT IN (SELECT request_id FROM offers WHERE vendor_id = ?) 
     ORDER BY pr.created_at DESC LIMIT 5",
    [$vendor_id]
)->fetchAll();

// أحدث العروض المقدمة
$recent_offers = $db->query(
    "SELECT o.*, pr.part_name, pr.car_make, pr.car_model, pr.car_year, u.name as customer_name 
     FROM offers o 
     JOIN part_requests pr ON o.request_id = pr.id 
     JOIN users u ON pr.customer_id = u.id 
     WHERE o.vendor_id = ? 
     ORDER BY o.created_at DESC LIMIT 5",
    [$vendor_id]
)->fetchAll();

// معلومات المحل
$vendor_profile = $db->query(
    "SELECT vp.*, u.name, u.email, u.phone FROM vendor_profiles vp 
     JOIN users u ON vp.user_id = u.id 
     WHERE vp.user_id = ?",
    [$vendor_id]
)->fetch();

$page_title = 'لوحة تحكم المحل';
include '../includes/header.php';
?>

<!-- معلومات المحل -->
<?php if ($vendor_profile): ?>
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h4 class="mb-1"><?php echo htmlspecialchars($vendor_profile['business_name'] ?: $vendor_profile['name']); ?></h4>
                <p class="text-muted mb-1">
                    <i class="fas fa-envelope me-1"></i> <?php echo htmlspecialchars($vendor_profile['email']); ?>
                    <span class="mx-2">|</span>
                    <i class="fas fa-phone me-1"></i> <?php echo htmlspecialchars($vendor_profile['phone']); ?>
                </p>
                <?php if ($vendor_profile['city']): ?>
                    <p class="text-muted mb-1">
                        <i class="fas fa-map-marker-alt me-1"></i> <?php echo htmlspecialchars($vendor_profile['city']); ?>
                    </p>
                <?php endif; ?>
                <?php if ($vendor_profile['rating'] > 0): ?>
                    <div class="d-flex align-items-center">
                        <span class="text-warning me-1">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <i class="fas fa-star<?php echo $i <= $vendor_profile['rating'] ? '' : '-o'; ?>"></i>
                            <?php endfor; ?>
                        </span>
                        <span class="text-muted">(<?php echo number_format($vendor_profile['rating'], 1); ?> من 5)</span>
                    </div>
                <?php endif; ?>
            </div>
            <div class="col-md-4 text-end">
                <a href="../profile.php" class="btn btn-outline-primary">
                    <i class="fas fa-edit me-1"></i>
                    تحديث الملف التجاري
                </a>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-handshake fs-1 mb-2"></i>
                <h3 class="mb-1"><?php echo $stats['total_offers']; ?></h3>
                <p class="mb-0">إجمالي العروض</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fs-1 mb-2"></i>
                <h3 class="mb-1"><?php echo $stats['accepted_offers']; ?></h3>
                <p class="mb-0">عروض مقبولة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-clock fs-1 mb-2"></i>
                <h3 class="mb-1"><?php echo $stats['pending_offers']; ?></h3>
                <p class="mb-0">في الانتظار</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-dollar-sign fs-1 mb-2"></i>
                <h3 class="mb-1"><?php echo formatPrice($stats['total_revenue']); ?></h3>
                <p class="mb-0">إجمالي المبيعات</p>
            </div>
        </div>
    </div>
</div>

<!-- تنبيه الطلبات الجديدة -->
<?php if ($stats['new_requests'] > 0): ?>
<div class="alert alert-info">
    <div class="d-flex align-items-center">
        <i class="fas fa-bell fs-4 me-3"></i>
        <div class="flex-grow-1">
            <h6 class="mb-1">طلبات جديدة متاحة!</h6>
            <p class="mb-0">يوجد <?php echo $stats['new_requests']; ?> طلب جديد يمكنك تقديم عرض عليه</p>
        </div>
        <a href="new-requests.php" class="btn btn-primary">
            <i class="fas fa-eye me-1"></i>
            عرض الطلبات
        </a>
    </div>
</div>
<?php endif; ?>

<!-- أزرار سريعة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title mb-3">إجراءات سريعة</h5>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="new-requests.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        طلبات جديدة (<?php echo $stats['new_requests']; ?>)
                    </a>
                    <a href="my-offers.php" class="btn btn-outline-primary">
                        <i class="fas fa-handshake me-2"></i>
                        عروضي
                    </a>
                    <a href="sales.php" class="btn btn-outline-success">
                        <i class="fas fa-chart-line me-2"></i>
                        المبيعات
                    </a>
                    <a href="../profile.php" class="btn btn-outline-secondary">
                        <i class="fas fa-user me-2"></i>
                        الملف التجاري
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الطلبات الجديدة المتاحة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-bell me-2"></i>
                    طلبات جديدة متاحة
                </h5>
                <a href="new-requests.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <?php if (empty($new_requests)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fs-1 mb-3"></i>
                        <p>لا توجد طلبات جديدة حالياً</p>
                        <small>ستظهر الطلبات الجديدة هنا عند إضافتها</small>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($new_requests as $request): ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1"><?php echo htmlspecialchars($request['part_name']); ?></h6>
                                        <p class="mb-1 text-muted small">
                                            <?php echo htmlspecialchars($request['car_make'] . ' ' . $request['car_model'] . ' ' . $request['car_year']); ?>
                                        </p>
                                        <p class="mb-1 text-muted small">
                                            العميل: <?php echo htmlspecialchars($request['customer_name']); ?>
                                        </p>
                                        <small class="text-muted">
                                            <?php echo formatArabicDate($request['created_at']); ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <a href="request-details.php?id=<?php echo $request['id']; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض وتقديم عرض
                                        </a>
                                        <?php if ($request['urgency'] === 'high'): ?>
                                            <br><span class="badge bg-danger mt-1">عاجل</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- أحدث العروض المقدمة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-handshake me-2"></i>
                    أحدث عروضي
                </h5>
                <a href="my-offers.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <?php if (empty($recent_offers)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-handshake fs-1 mb-3"></i>
                        <p>لم تقدم أي عروض بعد</p>
                        <a href="new-requests.php" class="btn btn-primary">تصفح الطلبات المتاحة</a>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_offers as $offer): ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1"><?php echo htmlspecialchars($offer['part_name']); ?></h6>
                                        <p class="mb-1 text-muted small">
                                            <?php echo htmlspecialchars($offer['car_make'] . ' ' . $offer['car_model'] . ' ' . $offer['car_year']); ?>
                                        </p>
                                        <p class="mb-1 text-muted small">
                                            العميل: <?php echo htmlspecialchars($offer['customer_name']); ?>
                                        </p>
                                        <small class="text-muted">
                                            <?php echo formatArabicDate($offer['created_at']); ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-success mb-1">
                                            <?php echo formatPrice($offer['price']); ?>
                                        </div>
                                        <span class="badge bg-<?php 
                                            echo $offer['status'] === 'pending' ? 'warning' : 
                                                ($offer['status'] === 'accepted' ? 'success' : 
                                                ($offer['status'] === 'rejected' ? 'danger' : 'secondary')); 
                                        ?>">
                                            <?php 
                                            $status_text = [
                                                'pending' => 'في الانتظار',
                                                'accepted' => 'مقبول',
                                                'rejected' => 'مرفوض',
                                                'expired' => 'منتهي الصلاحية'
                                            ];
                                            echo $status_text[$offer['status']] ?? $offer['status'];
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
