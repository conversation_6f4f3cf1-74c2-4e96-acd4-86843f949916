<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'vendor') {
    showAlert('يجب تسجيل الدخول كمحل للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();
$vendor_id = $_SESSION['user_id'];

// جلب المحادثات
$conversations = $db->query(
    "SELECT c.*, 
     o.price as offer_price, o.status as offer_status,
     pr.part_name, pr.car_make, pr.car_model, pr.car_year,
     cu.name as customer_name, cu.phone as customer_phone,
     m.content as last_message, m.created_at as last_message_time, m.sender_type as last_sender
     FROM conversations c
     JOIN offers o ON c.offer_id = o.id
     JOIN part_requests pr ON o.request_id = pr.id
     JOIN users cu ON c.customer_id = cu.id
     LEFT JOIN messages m ON c.last_message_id = m.id
     WHERE c.vendor_id = ? AND c.status = 'active'
     ORDER BY c.last_message_at DESC",
    [$vendor_id]
)->fetchAll();

// إحصائيات الرسائل
$stats = $db->query(
    "SELECT 
     COUNT(*) as total_conversations,
     SUM(vendor_unread_count) as total_unread
     FROM conversations 
     WHERE vendor_id = ? AND status = 'active'",
    [$vendor_id]
)->fetch();

$page_title = 'الرسائل والمحادثات';
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold text-primary">
        <i class="fas fa-comments me-2"></i>
        الرسائل والمحادثات
        <?php if ($stats['total_unread'] > 0): ?>
            <span class="badge bg-danger"><?php echo $stats['total_unread']; ?></span>
        <?php endif; ?>
    </h2>
    <div class="d-flex gap-2">
        <a href="dashboard.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            لوحة التحكم
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['total_conversations']; ?></h4>
                <p class="mb-0">محادثات نشطة</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['total_unread']; ?></h4>
                <p class="mb-0">رسائل غير مقروءة</p>
            </div>
        </div>
    </div>
</div>

<!-- قائمة المحادثات -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">محادثاتي مع العملاء</h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($conversations)): ?>
            <div class="text-center py-5">
                <i class="fas fa-comments fs-1 text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد محادثات</h5>
                <p class="text-muted">لم تبدأ أي محادثات مع العملاء بعد</p>
                <a href="new-requests.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    تصفح الطلبات الجديدة
                </a>
            </div>
        <?php else: ?>
            <div class="list-group list-group-flush">
                <?php foreach ($conversations as $conversation): ?>
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">
                                        <i class="fas fa-user me-2 text-primary"></i>
                                        <?php echo htmlspecialchars($conversation['customer_name']); ?>
                                        <?php if ($conversation['vendor_unread_count'] > 0): ?>
                                            <span class="badge bg-danger ms-2"><?php echo $conversation['vendor_unread_count']; ?></span>
                                        <?php endif; ?>
                                    </h6>
                                    <small class="text-muted">
                                        <?php echo $conversation['last_message_time'] ? formatArabicDate($conversation['last_message_time']) : 'لا توجد رسائل'; ?>
                                    </small>
                                </div>
                                
                                <div class="mb-2">
                                    <strong class="text-success">
                                        <?php echo htmlspecialchars($conversation['part_name']); ?>
                                    </strong>
                                    <span class="text-muted">
                                        - <?php echo htmlspecialchars($conversation['car_make'] . ' ' . $conversation['car_model'] . ' ' . $conversation['car_year']); ?>
                                    </span>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <span class="badge bg-success">
                                            عرضي: <?php echo formatPrice($conversation['offer_price']); ?>
                                        </span>
                                        <span class="badge bg-<?php 
                                            echo $conversation['offer_status'] === 'pending' ? 'warning' : 
                                                ($conversation['offer_status'] === 'accepted' ? 'success' : 'secondary'); 
                                        ?>">
                                            <?php 
                                            $status_text = [
                                                'pending' => 'في الانتظار',
                                                'accepted' => 'مقبول',
                                                'rejected' => 'مرفوض',
                                                'expired' => 'منتهي الصلاحية'
                                            ];
                                            echo $status_text[$conversation['offer_status']] ?? $conversation['offer_status'];
                                            ?>
                                        </span>
                                    </div>
                                    <div>
                                        <a href="tel:<?php echo htmlspecialchars($conversation['customer_phone']); ?>" class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-phone"></i>
                                        </a>
                                    </div>
                                </div>
                                
                                <?php if ($conversation['last_message']): ?>
                                    <p class="mb-1 text-muted">
                                        <i class="fas fa-<?php echo $conversation['last_sender'] === 'vendor' ? 'reply' : 'comment'; ?> me-1"></i>
                                        <?php 
                                        $message_preview = htmlspecialchars($conversation['last_message']);
                                        echo strlen($message_preview) > 100 ? substr($message_preview, 0, 100) . '...' : $message_preview;
                                        ?>
                                    </p>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="d-flex gap-2 mt-3">
                            <a href="chat.php?conversation_id=<?php echo $conversation['id']; ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-comments me-1"></i>
                                فتح المحادثة
                            </a>
                            
                            <?php if ($conversation['offer_status'] === 'pending'): ?>
                                <a href="offer-details.php?id=<?php echo $conversation['offer_id']; ?>" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-eye me-1"></i>
                                    تفاصيل العرض
                                </a>
                            <?php endif; ?>
                            
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="markAsRead(<?php echo $conversation['id']; ?>)">
                                <i class="fas fa-check me-1"></i>
                                تحديد كمقروء
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- نصائح للتواصل الفعال -->
<div class="card mt-4">
    <div class="card-header bg-success text-white">
        <h6 class="mb-0">
            <i class="fas fa-lightbulb me-2"></i>
            نصائح للتواصل الفعال مع العملاء
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        رد سريع على استفسارات العملاء
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        قدم معلومات مفصلة عن القطع
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        أرسل صور واضحة للقطع المتوفرة
                    </li>
                </ul>
            </div>
            <div class="col-md-6">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        وضح شروط الضمان والتوصيل
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        كن صادقاً حول حالة القطعة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        حافظ على التواصل المهذب والاحترافي
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function markAsRead(conversationId) {
    fetch('ajax/mark-conversation-read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            conversation_id: conversationId,
            user_type: 'vendor'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ أثناء تحديث حالة القراءة');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

// تحديث تلقائي للرسائل الجديدة كل 30 ثانية
setInterval(function() {
    fetch('ajax/check-new-messages.php')
    .then(response => response.json())
    .then(data => {
        if (data.has_new_messages) {
            const badge = document.querySelector('.badge.bg-danger');
            if (badge) {
                badge.textContent = data.total_unread;
            }
        }
    })
    .catch(error => console.error('Error checking messages:', error));
}, 30000);
</script>

<?php include '../includes/footer.php'; ?>
