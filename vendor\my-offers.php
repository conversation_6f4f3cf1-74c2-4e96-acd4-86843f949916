<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'vendor') {
    showAlert('يجب تسجيل الدخول كمحل للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();
$vendor_id = $_SESSION['user_id'];

// فلترة العروض
$status_filter = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

// بناء الاستعلام
$where_conditions = ["o.vendor_id = ?"];
$params = [$vendor_id];

if (!empty($status_filter)) {
    $where_conditions[] = "o.status = ?";
    $params[] = $status_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// عدد العروض الإجمالي
$count_query = "SELECT COUNT(*) as total FROM offers o WHERE $where_clause";
$total_offers = $db->query($count_query, $params)->fetch()['total'];
$total_pages = ceil($total_offers / $per_page);

// جلب العروض
$offers_query = "SELECT o.*, pr.part_name, pr.car_make, pr.car_model, pr.car_year, pr.status as request_status,
                 u.name as customer_name, u.phone as customer_phone
                 FROM offers o 
                 JOIN part_requests pr ON o.request_id = pr.id 
                 JOIN users u ON pr.customer_id = u.id 
                 WHERE $where_clause 
                 ORDER BY o.created_at DESC 
                 LIMIT $per_page OFFSET $offset";

$offers = $db->query($offers_query, $params)->fetchAll();

$page_title = 'عروضي';
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold text-primary">
        <i class="fas fa-handshake me-2"></i>
        عروضي
    </h2>
    <div class="d-flex gap-2">
        <a href="new-requests.php" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            تقديم عرض جديد
        </a>
        <a href="dashboard.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            لوحة التحكم
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <?php
    $stats = [];
    $stats['total'] = $db->query("SELECT COUNT(*) as count FROM offers WHERE vendor_id = ?", [$vendor_id])->fetch()['count'];
    $stats['pending'] = $db->query("SELECT COUNT(*) as count FROM offers WHERE vendor_id = ? AND status = 'pending'", [$vendor_id])->fetch()['count'];
    $stats['accepted'] = $db->query("SELECT COUNT(*) as count FROM offers WHERE vendor_id = ? AND status = 'accepted'", [$vendor_id])->fetch()['count'];
    $stats['rejected'] = $db->query("SELECT COUNT(*) as count FROM offers WHERE vendor_id = ? AND status = 'rejected'", [$vendor_id])->fetch()['count'];
    ?>
    
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['total']; ?></h4>
                <p class="mb-0">إجمالي العروض</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['pending']; ?></h4>
                <p class="mb-0">في الانتظار</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['accepted']; ?></h4>
                <p class="mb-0">مقبولة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['rejected']; ?></h4>
                <p class="mb-0">مرفوضة</p>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">حالة العرض</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                    <option value="accepted" <?php echo $status_filter === 'accepted' ? 'selected' : ''; ?>>مقبول</option>
                    <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>مرفوض</option>
                    <option value="expired" <?php echo $status_filter === 'expired' ? 'selected' : ''; ?>>منتهي الصلاحية</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>
                        فلترة
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة العروض -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">عروضي (<?php echo $total_offers; ?>)</h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($offers)): ?>
            <div class="text-center py-5">
                <i class="fas fa-handshake fs-1 text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد عروض</h5>
                <p class="text-muted">لم تقم بتقديم أي عروض بعد</p>
                <a href="new-requests.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    تصفح الطلبات المتاحة
                </a>
            </div>
        <?php else: ?>
            <div class="list-group list-group-flush">
                <?php foreach ($offers as $offer): ?>
                    <div class="list-group-item">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <h6 class="mb-1 fw-bold"><?php echo htmlspecialchars($offer['part_name']); ?></h6>
                                <p class="mb-1 text-muted">
                                    <i class="fas fa-car me-1"></i>
                                    <?php echo htmlspecialchars($offer['car_make'] . ' ' . $offer['car_model'] . ' ' . $offer['car_year']); ?>
                                </p>
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>
                                    العميل: <?php echo htmlspecialchars($offer['customer_name']); ?>
                                </small>
                            </div>
                            
                            <div class="col-md-2 text-center">
                                <div class="fw-bold text-success fs-5"><?php echo formatPrice($offer['price']); ?></div>
                                <?php if ($offer['delivery_cost'] > 0): ?>
                                    <small class="text-muted">+ <?php echo formatPrice($offer['delivery_cost']); ?> توصيل</small>
                                <?php endif; ?>
                            </div>
                            
                            <div class="col-md-2 text-center">
                                <span class="badge bg-<?php 
                                    echo $offer['condition_type'] === 'new' ? 'success' : 'warning'; 
                                ?>">
                                    <?php echo $offer['condition_type'] === 'new' ? 'جديد' : 'مستعمل'; ?>
                                </span>
                                <?php if ($offer['warranty_period'] > 0): ?>
                                    <br><small class="text-muted">ضمان <?php echo $offer['warranty_period']; ?> شهر</small>
                                <?php endif; ?>
                            </div>
                            
                            <div class="col-md-2 text-center">
                                <span class="badge bg-<?php 
                                    echo $offer['status'] === 'pending' ? 'warning' : 
                                        ($offer['status'] === 'accepted' ? 'success' : 
                                        ($offer['status'] === 'rejected' ? 'danger' : 'secondary')); 
                                ?> fs-6">
                                    <?php 
                                    $status_text = [
                                        'pending' => 'في الانتظار',
                                        'accepted' => 'مقبول',
                                        'rejected' => 'مرفوض',
                                        'expired' => 'منتهي الصلاحية'
                                    ];
                                    echo $status_text[$offer['status']] ?? $offer['status'];
                                    ?>
                                </span>
                                <br>
                                <small class="text-muted">
                                    <?php echo formatArabicDate($offer['created_at']); ?>
                                </small>
                            </div>
                            
                            <div class="col-md-2 text-end">
                                <div class="btn-group-vertical">
                                    <a href="offer-details.php?id=<?php echo $offer['id']; ?>" class="btn btn-sm btn-outline-primary mb-1">
                                        <i class="fas fa-eye"></i>
                                        عرض التفاصيل
                                    </a>
                                    
                                    <?php if ($offer['status'] === 'accepted'): ?>
                                        <a href="tel:<?php echo htmlspecialchars($offer['customer_phone']); ?>" class="btn btn-sm btn-success">
                                            <i class="fas fa-phone"></i>
                                            اتصال بالعميل
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($offer['notes']): ?>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <strong>ملاحظاتي:</strong> <?php echo htmlspecialchars($offer['notes']); ?>
                                </small>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($offer['delivery_time']): ?>
                            <div class="mt-1">
                                <small class="text-muted">
                                    <i class="fas fa-truck me-1"></i>
                                    وقت التوصيل: <?php echo htmlspecialchars($offer['delivery_time']); ?>
                                </small>
                            </div>
                        <?php endif; ?>
                        
                        <!-- تنبيهات خاصة -->
                        <?php if ($offer['status'] === 'accepted' && $offer['request_status'] === 'closed'): ?>
                            <div class="mt-2">
                                <div class="alert alert-success alert-sm mb-0">
                                    <i class="fas fa-check-circle me-1"></i>
                                    <strong>تهانينا!</strong> تم قبول عرضك. يرجى التواصل مع العميل لإتمام الصفقة.
                                </div>
                            </div>
                        <?php elseif ($offer['status'] === 'pending' && strtotime($offer['expires_at']) < time()): ?>
                            <div class="mt-2">
                                <div class="alert alert-warning alert-sm mb-0">
                                    <i class="fas fa-clock me-1"></i>
                                    هذا العرض انتهت صلاحيته في <?php echo formatArabicDate($offer['expires_at']); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- ترقيم الصفحات -->
    <?php if ($total_pages > 1): ?>
        <div class="card-footer">
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>">السابق</a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>"><?php echo $i; ?></a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>">التالي</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    <?php endif; ?>
</div>

<style>
.alert-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}
</style>

<?php include '../includes/footer.php'; ?>
