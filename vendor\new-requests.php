<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'vendor') {
    showAlert('يجب تسجيل الدخول كمحل للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();
$vendor_id = $_SESSION['user_id'];

// فلترة الطلبات
$car_make_filter = $_GET['car_make'] ?? '';
$urgency_filter = $_GET['urgency'] ?? '';
$condition_filter = $_GET['condition'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

// بناء الاستعلام - الطلبات التي لم يقدم عليها هذا المحل عرض
$where_conditions = [
    "pr.status IN ('pending', 'active')",
    "pr.id NOT IN (SELECT request_id FROM offers WHERE vendor_id = ?)"
];
$params = [$vendor_id];

if (!empty($car_make_filter)) {
    $where_conditions[] = "pr.car_make = ?";
    $params[] = $car_make_filter;
}

if (!empty($urgency_filter)) {
    $where_conditions[] = "pr.urgency = ?";
    $params[] = $urgency_filter;
}

if (!empty($condition_filter)) {
    $where_conditions[] = "pr.condition_type IN (?, 'both')";
    $params[] = $condition_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// عدد الطلبات الإجمالي
$count_query = "SELECT COUNT(*) as total FROM part_requests pr 
                JOIN users u ON pr.customer_id = u.id 
                WHERE $where_clause";
$total_requests = $db->query($count_query, $params)->fetch()['total'];
$total_pages = ceil($total_requests / $per_page);

// جلب الطلبات
$requests_query = "SELECT pr.*, u.name as customer_name, u.phone as customer_phone,
                   (SELECT COUNT(*) FROM offers WHERE request_id = pr.id) as offers_count
                   FROM part_requests pr 
                   JOIN users u ON pr.customer_id = u.id 
                   WHERE $where_clause 
                   ORDER BY 
                   CASE pr.urgency 
                       WHEN 'high' THEN 1 
                       WHEN 'medium' THEN 2 
                       WHEN 'low' THEN 3 
                   END,
                   pr.created_at DESC 
                   LIMIT $per_page OFFSET $offset";

$requests = $db->query($requests_query, $params)->fetchAll();

// جلب ماركات السيارات للفلترة
$car_makes = $db->query(
    "SELECT DISTINCT car_make FROM part_requests 
     WHERE status IN ('pending', 'active') 
     ORDER BY car_make"
)->fetchAll();

$page_title = 'الطلبات الجديدة المتاحة';
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold text-primary">
        <i class="fas fa-bell me-2"></i>
        الطلبات الجديدة المتاحة
    </h2>
    <div class="d-flex gap-2">
        <a href="my-offers.php" class="btn btn-outline-primary">
            <i class="fas fa-handshake me-1"></i>
            عروضي
        </a>
        <a href="dashboard.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            لوحة التحكم
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <?php
    $stats = [];
    $stats['total'] = $total_requests;
    $stats['high_priority'] = $db->query("SELECT COUNT(*) as count FROM part_requests pr WHERE pr.status IN ('pending', 'active') AND pr.urgency = 'high' AND pr.id NOT IN (SELECT request_id FROM offers WHERE vendor_id = ?)", [$vendor_id])->fetch()['count'];
    $stats['today'] = $db->query("SELECT COUNT(*) as count FROM part_requests pr WHERE pr.status IN ('pending', 'active') AND DATE(pr.created_at) = CURDATE() AND pr.id NOT IN (SELECT request_id FROM offers WHERE vendor_id = ?)", [$vendor_id])->fetch()['count'];
    $stats['with_budget'] = $db->query("SELECT COUNT(*) as count FROM part_requests pr WHERE pr.status IN ('pending', 'active') AND (pr.budget_min > 0 OR pr.budget_max > 0) AND pr.id NOT IN (SELECT request_id FROM offers WHERE vendor_id = ?)", [$vendor_id])->fetch()['count'];
    ?>
    
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['total']; ?></h4>
                <p class="mb-0">طلبات متاحة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['high_priority']; ?></h4>
                <p class="mb-0">عاجلة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['today']; ?></h4>
                <p class="mb-0">اليوم</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4><?php echo $stats['with_budget']; ?></h4>
                <p class="mb-0">بميزانية محددة</p>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">ماركة السيارة</label>
                <select name="car_make" class="form-select">
                    <option value="">جميع الماركات</option>
                    <?php foreach ($car_makes as $make): ?>
                        <option value="<?php echo htmlspecialchars($make['car_make']); ?>" <?php echo $car_make_filter === $make['car_make'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($make['car_make']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">الأولوية</label>
                <select name="urgency" class="form-select">
                    <option value="">جميع الأولويات</option>
                    <option value="high" <?php echo $urgency_filter === 'high' ? 'selected' : ''; ?>>عاجل</option>
                    <option value="medium" <?php echo $urgency_filter === 'medium' ? 'selected' : ''; ?>>متوسط</option>
                    <option value="low" <?php echo $urgency_filter === 'low' ? 'selected' : ''; ?>>عادي</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">حالة القطعة</label>
                <select name="condition" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="new" <?php echo $condition_filter === 'new' ? 'selected' : ''; ?>>جديدة</option>
                    <option value="used" <?php echo $condition_filter === 'used' ? 'selected' : ''; ?>>مستعملة</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>
                        فلترة
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الطلبات -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">الطلبات المتاحة (<?php echo $total_requests; ?>)</h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($requests)): ?>
            <div class="text-center py-5">
                <i class="fas fa-inbox fs-1 text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد طلبات جديدة</h5>
                <p class="text-muted">لا توجد طلبات متاحة حالياً أو قمت بتقديم عروض على جميع الطلبات</p>
                <a href="dashboard.php" class="btn btn-primary">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    العودة للوحة التحكم
                </a>
            </div>
        <?php else: ?>
            <div class="list-group list-group-flush">
                <?php foreach ($requests as $request): ?>
                    <div class="list-group-item">
                        <div class="row align-items-center">
                            <div class="col-md-5">
                                <div class="d-flex align-items-start">
                                    <?php if ($request['urgency'] === 'high'): ?>
                                        <span class="badge bg-danger me-2 mt-1">عاجل</span>
                                    <?php endif; ?>
                                    <div>
                                        <h6 class="mb-1 fw-bold"><?php echo htmlspecialchars($request['part_name']); ?></h6>
                                        <p class="mb-1 text-muted">
                                            <i class="fas fa-car me-1"></i>
                                            <?php echo htmlspecialchars($request['car_make'] . ' ' . $request['car_model'] . ' ' . $request['car_year']); ?>
                                        </p>
                                        <?php if ($request['part_number']): ?>
                                            <p class="mb-1 text-muted small">
                                                <i class="fas fa-barcode me-1"></i>
                                                رقم القطعة: <?php echo htmlspecialchars($request['part_number']); ?>
                                            </p>
                                        <?php endif; ?>
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>
                                            العميل: <?php echo htmlspecialchars($request['customer_name']); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-2 text-center">
                                <span class="badge bg-<?php 
                                    echo $request['condition_type'] === 'new' ? 'success' : 
                                        ($request['condition_type'] === 'used' ? 'warning' : 'info'); 
                                ?>">
                                    <?php 
                                    $condition_text = [
                                        'new' => 'جديدة فقط',
                                        'used' => 'مستعملة فقط',
                                        'both' => 'جديدة أو مستعملة'
                                    ];
                                    echo $condition_text[$request['condition_type']] ?? $request['condition_type'];
                                    ?>
                                </span>
                            </div>
                            
                            <div class="col-md-2 text-center">
                                <?php if ($request['budget_min'] || $request['budget_max']): ?>
                                    <div class="fw-bold text-success">
                                        <?php if ($request['budget_min'] && $request['budget_max']): ?>
                                            <?php echo formatPrice($request['budget_min']) . ' - ' . formatPrice($request['budget_max']); ?>
                                        <?php elseif ($request['budget_max']): ?>
                                            حتى <?php echo formatPrice($request['budget_max']); ?>
                                        <?php elseif ($request['budget_min']): ?>
                                            من <?php echo formatPrice($request['budget_min']); ?>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted">الميزانية</small>
                                <?php else: ?>
                                    <span class="text-muted">غير محددة</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="col-md-2 text-center">
                                <div class="fw-bold text-primary"><?php echo $request['offers_count']; ?></div>
                                <small class="text-muted">عروض أخرى</small>
                                <br>
                                <small class="text-muted">
                                    <?php echo formatArabicDate($request['created_at']); ?>
                                </small>
                            </div>
                            
                            <div class="col-md-1 text-end">
                                <a href="request-details.php?id=<?php echo $request['id']; ?>" class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>
                                    عرض وتقديم عرض
                                </a>
                            </div>
                        </div>
                        
                        <?php if ($request['description']): ?>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <strong>الوصف:</strong> <?php echo htmlspecialchars(substr($request['description'], 0, 150)); ?>
                                    <?php if (strlen($request['description']) > 150): ?>...<?php endif; ?>
                                </small>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($request['location']): ?>
                            <div class="mt-1">
                                <small class="text-muted">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    الموقع: <?php echo htmlspecialchars($request['location']); ?>
                                </small>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- ترقيم الصفحات -->
    <?php if ($total_pages > 1): ?>
        <div class="card-footer">
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&car_make=<?php echo urlencode($car_make_filter); ?>&urgency=<?php echo $urgency_filter; ?>&condition=<?php echo $condition_filter; ?>">السابق</a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>&car_make=<?php echo urlencode($car_make_filter); ?>&urgency=<?php echo $urgency_filter; ?>&condition=<?php echo $condition_filter; ?>"><?php echo $i; ?></a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&car_make=<?php echo urlencode($car_make_filter); ?>&urgency=<?php echo $urgency_filter; ?>&condition=<?php echo $condition_filter; ?>">التالي</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>
