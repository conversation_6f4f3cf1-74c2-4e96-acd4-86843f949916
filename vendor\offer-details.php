<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'vendor') {
    showAlert('يجب تسجيل الدخول كمحل للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();
$vendor_id = $_SESSION['user_id'];
$offer_id = intval($_GET['id'] ?? 0);

if (!$offer_id) {
    showAlert('عرض غير صحيح', 'error');
    redirect('my-offers.php');
}

// جلب تفاصيل العرض
$offer = $db->query(
    "SELECT o.*, pr.part_name, pr.car_make, pr.car_model, pr.car_year, pr.description as request_description,
     pr.condition_type as request_condition, pr.urgency, pr.budget_min, pr.budget_max, pr.location,
     pr.images as request_images, pr.status as request_status,
     u.name as customer_name, u.phone as customer_phone, u.email as customer_email
     FROM offers o 
     JOIN part_requests pr ON o.request_id = pr.id 
     JOIN users u ON pr.customer_id = u.id 
     WHERE o.id = ? AND o.vendor_id = ?",
    [$offer_id, $vendor_id]
)->fetch();

if (!$offer) {
    showAlert('العرض غير موجود', 'error');
    redirect('my-offers.php');
}

$page_title = 'تفاصيل العرض';
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold text-primary">
        <i class="fas fa-eye me-2"></i>
        تفاصيل العرض
    </h2>
    <div class="d-flex gap-2">
        <a href="my-offers.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة لعروضي
        </a>
    </div>
</div>

<div class="row">
    <!-- تفاصيل العرض -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-handshake me-2"></i>
                    تفاصيل عرضي
                </h5>
                <span class="badge bg-<?php 
                    echo $offer['status'] === 'pending' ? 'warning' : 
                        ($offer['status'] === 'accepted' ? 'success' : 
                        ($offer['status'] === 'rejected' ? 'danger' : 'secondary')); 
                ?> fs-6">
                    <?php 
                    $status_text = [
                        'pending' => 'في الانتظار',
                        'accepted' => 'مقبول',
                        'rejected' => 'مرفوض',
                        'expired' => 'منتهي الصلاحية'
                    ];
                    echo $status_text[$offer['status']] ?? $offer['status'];
                    ?>
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">السعر المقترح:</label>
                        <p class="mb-0 fs-4 text-success fw-bold"><?php echo formatPrice($offer['price']); ?></p>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">حالة القطعة:</label>
                        <p class="mb-0">
                            <span class="badge bg-<?php echo $offer['condition_type'] === 'new' ? 'success' : 'warning'; ?> fs-6">
                                <?php echo $offer['condition_type'] === 'new' ? 'جديدة' : 'مستعملة'; ?>
                            </span>
                        </p>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">فترة الضمان:</label>
                        <p class="mb-0">
                            <?php if ($offer['warranty_period'] > 0): ?>
                                <?php echo $offer['warranty_period']; ?> شهر
                            <?php else: ?>
                                بدون ضمان
                            <?php endif; ?>
                        </p>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تكلفة التوصيل:</label>
                        <p class="mb-0">
                            <?php if ($offer['delivery_cost'] > 0): ?>
                                <?php echo formatPrice($offer['delivery_cost']); ?>
                            <?php else: ?>
                                مجاني
                            <?php endif; ?>
                        </p>
                    </div>
                    
                    <?php if ($offer['delivery_time']): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">وقت التوصيل:</label>
                            <p class="mb-0"><?php echo htmlspecialchars($offer['delivery_time']); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تاريخ تقديم العرض:</label>
                        <p class="mb-0"><?php echo formatArabicDate($offer['created_at']); ?></p>
                    </div>
                    
                    <?php if ($offer['expires_at']): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">تاريخ انتهاء الصلاحية:</label>
                            <p class="mb-0 <?php echo strtotime($offer['expires_at']) < time() ? 'text-danger' : 'text-success'; ?>">
                                <?php echo formatArabicDate($offer['expires_at']); ?>
                                <?php if (strtotime($offer['expires_at']) < time()): ?>
                                    <span class="badge bg-danger ms-1">منتهي الصلاحية</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($offer['notes']): ?>
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold">ملاحظاتي:</label>
                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($offer['notes'])); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- صور العرض -->
                <?php if ($offer['images']): ?>
                    <div class="mt-3">
                        <label class="form-label fw-bold">صور القطعة المتوفرة:</label>
                        <div class="row">
                            <?php 
                            $images = json_decode($offer['images'], true);
                            if ($images):
                                foreach ($images as $image): 
                            ?>
                                <div class="col-md-3 mb-2">
                                    <img src="../uploads/offers/<?php echo htmlspecialchars($image); ?>" 
                                         class="img-thumbnail" 
                                         style="width: 100%; height: 150px; object-fit: cover; cursor: pointer;"
                                         data-bs-toggle="modal" 
                                         data-bs-target="#imageModal"
                                         onclick="showImage('../uploads/offers/<?php echo htmlspecialchars($image); ?>')">
                                </div>
                            <?php 
                                endforeach;
                            endif; 
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- تفاصيل الطلب الأصلي -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل الطلب الأصلي
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">اسم القطعة:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($offer['part_name']); ?></p>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">السيارة:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($offer['car_make'] . ' ' . $offer['car_model'] . ' ' . $offer['car_year']); ?></p>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">حالة القطعة المطلوبة:</label>
                        <p class="mb-0">
                            <span class="badge bg-<?php 
                                echo $offer['request_condition'] === 'new' ? 'success' : 
                                    ($offer['request_condition'] === 'used' ? 'warning' : 'info'); 
                            ?>">
                                <?php 
                                $condition_text = [
                                    'new' => 'جديدة فقط',
                                    'used' => 'مستعملة فقط',
                                    'both' => 'جديدة أو مستعملة'
                                ];
                                echo $condition_text[$offer['request_condition']] ?? $offer['request_condition'];
                                ?>
                            </span>
                        </p>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">الأولوية:</label>
                        <p class="mb-0">
                            <span class="badge bg-<?php 
                                echo $offer['urgency'] === 'high' ? 'danger' : 
                                    ($offer['urgency'] === 'medium' ? 'warning' : 'secondary'); 
                            ?>">
                                <?php 
                                $urgency_text = [
                                    'high' => 'عاجل',
                                    'medium' => 'متوسط',
                                    'low' => 'عادي'
                                ];
                                echo $urgency_text[$offer['urgency']] ?? $offer['urgency'];
                                ?>
                            </span>
                        </p>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">ميزانية العميل:</label>
                        <p class="mb-0">
                            <?php if ($offer['budget_min'] || $offer['budget_max']): ?>
                                <?php if ($offer['budget_min'] && $offer['budget_max']): ?>
                                    <?php echo formatPrice($offer['budget_min']) . ' - ' . formatPrice($offer['budget_max']); ?>
                                <?php elseif ($offer['budget_max']): ?>
                                    حتى <?php echo formatPrice($offer['budget_max']); ?>
                                <?php elseif ($offer['budget_min']): ?>
                                    من <?php echo formatPrice($offer['budget_min']); ?>
                                <?php endif; ?>
                            <?php else: ?>
                                غير محددة
                            <?php endif; ?>
                        </p>
                    </div>
                    
                    <?php if ($offer['location']): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">موقع العميل:</label>
                            <p class="mb-0">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?php echo htmlspecialchars($offer['location']); ?>
                            </p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($offer['request_description']): ?>
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold">وصف العميل للقطعة:</label>
                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($offer['request_description'])); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- صور الطلب الأصلي -->
                <?php if ($offer['request_images']): ?>
                    <div class="mt-3">
                        <label class="form-label fw-bold">صور العميل للقطعة المطلوبة:</label>
                        <div class="row">
                            <?php 
                            $request_images = json_decode($offer['request_images'], true);
                            if ($request_images):
                                foreach ($request_images as $image): 
                            ?>
                                <div class="col-md-3 mb-2">
                                    <img src="../uploads/requests/<?php echo htmlspecialchars($image); ?>" 
                                         class="img-thumbnail" 
                                         style="width: 100%; height: 150px; object-fit: cover; cursor: pointer;"
                                         data-bs-toggle="modal" 
                                         data-bs-target="#imageModal"
                                         onclick="showImage('../uploads/requests/<?php echo htmlspecialchars($image); ?>')">
                                </div>
                            <?php 
                                endforeach;
                            endif; 
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- معلومات العميل والإجراءات -->
    <div class="col-lg-4">
        <!-- معلومات العميل -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات العميل
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 60px; height: 60px;">
                        <i class="fas fa-user fs-3"></i>
                    </div>
                    <h6 class="fw-bold"><?php echo htmlspecialchars($offer['customer_name']); ?></h6>
                </div>
                
                <div class="d-grid gap-2">
                    <a href="tel:<?php echo htmlspecialchars($offer['customer_phone']); ?>" class="btn btn-success">
                        <i class="fas fa-phone me-2"></i>
                        <?php echo htmlspecialchars($offer['customer_phone']); ?>
                    </a>
                    
                    <a href="mailto:<?php echo htmlspecialchars($offer['customer_email']); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-envelope me-2"></i>
                        إرسال إيميل
                    </a>
                    
                    <?php if ($offer['status'] === 'accepted'): ?>
                        <div class="alert alert-success text-center mt-3">
                            <i class="fas fa-check-circle fs-2 mb-2"></i>
                            <h6>تهانينا!</h6>
                            <p class="small mb-0">تم قبول عرضك. يرجى التواصل مع العميل لإتمام الصفقة.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- حالة العرض -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    حالة العرض
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <?php if ($offer['status'] === 'pending'): ?>
                        <i class="fas fa-clock fs-1 text-warning mb-3"></i>
                        <h6 class="text-warning">في انتظار رد العميل</h6>
                        <p class="text-muted small">العميل لم يرد على عرضك بعد</p>
                    <?php elseif ($offer['status'] === 'accepted'): ?>
                        <i class="fas fa-check-circle fs-1 text-success mb-3"></i>
                        <h6 class="text-success">تم قبول العرض</h6>
                        <p class="text-muted small">العميل قبل عرضك، يرجى التواصل معه</p>
                    <?php elseif ($offer['status'] === 'rejected'): ?>
                        <i class="fas fa-times-circle fs-1 text-danger mb-3"></i>
                        <h6 class="text-danger">تم رفض العرض</h6>
                        <p class="text-muted small">العميل رفض عرضك</p>
                    <?php else: ?>
                        <i class="fas fa-exclamation-circle fs-1 text-secondary mb-3"></i>
                        <h6 class="text-secondary">منتهي الصلاحية</h6>
                        <p class="text-muted small">انتهت صلاحية العرض</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- إجراءات سريعة -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="new-requests.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        تصفح طلبات جديدة
                    </a>
                    
                    <a href="my-offers.php" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>
                        جميع عروضي
                    </a>
                    
                    <a href="dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض الصور -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">صورة القطعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<script>
function showImage(src) {
    document.getElementById('modalImage').src = src;
}
</script>

<?php include '../includes/footer.php'; ?>
