<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول ونوع المستخدم
if (!isLoggedIn() || getUserType() !== 'vendor') {
    showAlert('يجب تسجيل الدخول كمحل للوصول لهذه الصفحة', 'error');
    redirect('../login.php');
}

$db = getDB();
$vendor_id = $_SESSION['user_id'];
$request_id = intval($_GET['id'] ?? 0);

if (!$request_id) {
    showAlert('طلب غير صحيح', 'error');
    redirect('new-requests.php');
}

// جلب تفاصيل الطلب
$request = $db->query(
    "SELECT pr.*, u.name as customer_name, u.phone as customer_phone, u.email as customer_email
     FROM part_requests pr 
     JOIN users u ON pr.customer_id = u.id 
     WHERE pr.id = ?",
    [$request_id]
)->fetch();

if (!$request) {
    showAlert('الطلب غير موجود', 'error');
    redirect('new-requests.php');
}

// التحقق من أن المحل لم يقدم عرض على هذا الطلب مسبقاً
$existing_offer = $db->query(
    "SELECT id FROM offers WHERE request_id = ? AND vendor_id = ?",
    [$request_id, $vendor_id]
)->fetch();

// جلب العروض الأخرى على هذا الطلب
$other_offers = $db->query(
    "SELECT o.*, u.name as vendor_name, vp.business_name 
     FROM offers o 
     JOIN users u ON o.vendor_id = u.id 
     LEFT JOIN vendor_profiles vp ON u.id = vp.user_id 
     WHERE o.request_id = ? AND o.vendor_id != ? 
     ORDER BY o.price ASC",
    [$request_id, $vendor_id]
)->fetchAll();

$errors = [];
$success = false;

// معالجة تقديم العرض
if ($_POST && !$existing_offer) {
    $price = floatval($_POST['price'] ?? 0);
    $condition_type = sanitize($_POST['condition_type'] ?? '');
    $warranty_period = intval($_POST['warranty_period'] ?? 0);
    $delivery_time = sanitize($_POST['delivery_time'] ?? '');
    $delivery_cost = floatval($_POST['delivery_cost'] ?? 0);
    $notes = sanitize($_POST['notes'] ?? '');
    
    // التحقق من البيانات
    if ($price <= 0) {
        $errors[] = 'السعر مطلوب ويجب أن يكون أكبر من صفر';
    }
    
    if (empty($condition_type) || !in_array($condition_type, ['new', 'used'])) {
        $errors[] = 'حالة القطعة مطلوبة';
    }
    
    // التحقق من توافق حالة القطعة مع طلب العميل
    if ($request['condition_type'] !== 'both' && $request['condition_type'] !== $condition_type) {
        $errors[] = 'حالة القطعة المقترحة لا تتوافق مع طلب العميل';
    }
    
    if ($warranty_period < 0 || $warranty_period > 60) {
        $errors[] = 'فترة الضمان يجب أن تكون بين 0 و 60 شهر';
    }
    
    if ($delivery_cost < 0) {
        $errors[] = 'تكلفة التوصيل لا يمكن أن تكون سالبة';
    }
    
    // معالجة رفع الصور
    $uploaded_images = [];
    if (isset($_FILES['images']) && !empty($_FILES['images']['name'][0])) {
        if (!is_dir('../uploads/offers/')) {
            mkdir('../uploads/offers/', 0755, true);
        }
        
        for ($i = 0; $i < count($_FILES['images']['name']); $i++) {
            if ($_FILES['images']['error'][$i] === UPLOAD_ERR_OK) {
                $file = [
                    'name' => $_FILES['images']['name'][$i],
                    'tmp_name' => $_FILES['images']['tmp_name'][$i],
                    'size' => $_FILES['images']['size'][$i],
                    'error' => $_FILES['images']['error'][$i]
                ];
                
                $filename = uploadFile($file, '../uploads/offers/');
                if ($filename) {
                    $uploaded_images[] = $filename;
                }
            }
        }
    }
    
    // إنشاء العرض
    if (empty($errors)) {
        $expires_at = date('Y-m-d H:i:s', strtotime('+3 days')); // انتهاء الصلاحية بعد 3 أيام
        $images_json = !empty($uploaded_images) ? json_encode($uploaded_images) : null;
        
        $stmt = $db->query(
            "INSERT INTO offers (request_id, vendor_id, price, condition_type, warranty_period, delivery_time, delivery_cost, notes, images, expires_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            [
                $request_id,
                $vendor_id,
                $price,
                $condition_type,
                $warranty_period,
                $delivery_time,
                $delivery_cost,
                $notes,
                $images_json,
                $expires_at
            ]
        );
        
        if ($stmt) {
            // تحديث حالة الطلب إلى نشط إذا كان في الانتظار
            if ($request['status'] === 'pending') {
                $db->query("UPDATE part_requests SET status = 'active' WHERE id = ?", [$request_id]);
            }
            
            $success = true;
            showAlert('تم تقديم عرضك بنجاح! سيتم إشعار العميل.', 'success');
            redirect('my-offers.php');
        } else {
            $errors[] = 'حدث خطأ أثناء تقديم العرض';
        }
    }
}

$page_title = 'تفاصيل الطلب';
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold text-primary">
        <i class="fas fa-eye me-2"></i>
        تفاصيل الطلب
    </h2>
    <div class="d-flex gap-2">
        <a href="new-requests.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للطلبات
        </a>
    </div>
</div>

<div class="row">
    <!-- تفاصيل الطلب -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل الطلب
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">اسم القطعة:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($request['part_name']); ?></p>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">رقم القطعة:</label>
                        <p class="mb-0"><?php echo $request['part_number'] ? htmlspecialchars($request['part_number']) : 'غير محدد'; ?></p>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">ماركة السيارة:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($request['car_make']); ?></p>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">الموديل:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($request['car_model']); ?></p>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">السنة:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($request['car_year']); ?></p>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">حالة القطعة المطلوبة:</label>
                        <p class="mb-0">
                            <span class="badge bg-<?php 
                                echo $request['condition_type'] === 'new' ? 'success' : 
                                    ($request['condition_type'] === 'used' ? 'warning' : 'info'); 
                            ?>">
                                <?php 
                                $condition_text = [
                                    'new' => 'جديدة فقط',
                                    'used' => 'مستعملة فقط',
                                    'both' => 'جديدة أو مستعملة'
                                ];
                                echo $condition_text[$request['condition_type']] ?? $request['condition_type'];
                                ?>
                            </span>
                        </p>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">الأولوية:</label>
                        <p class="mb-0">
                            <span class="badge bg-<?php 
                                echo $request['urgency'] === 'high' ? 'danger' : 
                                    ($request['urgency'] === 'medium' ? 'warning' : 'secondary'); 
                            ?>">
                                <?php 
                                $urgency_text = [
                                    'high' => 'عاجل',
                                    'medium' => 'متوسط',
                                    'low' => 'عادي'
                                ];
                                echo $urgency_text[$request['urgency']] ?? $request['urgency'];
                                ?>
                            </span>
                        </p>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">الميزانية:</label>
                        <p class="mb-0">
                            <?php if ($request['budget_min'] || $request['budget_max']): ?>
                                <?php if ($request['budget_min'] && $request['budget_max']): ?>
                                    <?php echo formatPrice($request['budget_min']) . ' - ' . formatPrice($request['budget_max']); ?>
                                <?php elseif ($request['budget_max']): ?>
                                    حتى <?php echo formatPrice($request['budget_max']); ?>
                                <?php elseif ($request['budget_min']): ?>
                                    من <?php echo formatPrice($request['budget_min']); ?>
                                <?php endif; ?>
                            <?php else: ?>
                                غير محددة
                            <?php endif; ?>
                        </p>
                    </div>
                    
                    <?php if ($request['location']): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">الموقع:</label>
                            <p class="mb-0">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?php echo htmlspecialchars($request['location']); ?>
                            </p>
                        </div>
                    <?php endif; ?>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تاريخ الطلب:</label>
                        <p class="mb-0"><?php echo formatArabicDate($request['created_at']); ?></p>
                    </div>
                    
                    <?php if ($request['description']): ?>
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold">الوصف:</label>
                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($request['description'])); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- صور الطلب -->
                <?php if ($request['images']): ?>
                    <div class="mt-3">
                        <label class="form-label fw-bold">صور القطعة:</label>
                        <div class="row">
                            <?php 
                            $images = json_decode($request['images'], true);
                            if ($images):
                                foreach ($images as $image): 
                            ?>
                                <div class="col-md-3 mb-2">
                                    <img src="../uploads/requests/<?php echo htmlspecialchars($image); ?>" 
                                         class="img-thumbnail" 
                                         style="width: 100%; height: 150px; object-fit: cover;"
                                         data-bs-toggle="modal" 
                                         data-bs-target="#imageModal"
                                         onclick="showImage('../uploads/requests/<?php echo htmlspecialchars($image); ?>')">
                                </div>
                            <?php 
                                endforeach;
                            endif; 
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- معلومات العميل -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات العميل
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label fw-bold">الاسم:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($request['customer_name']); ?></p>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label fw-bold">الهاتف:</label>
                        <p class="mb-0">
                            <a href="tel:<?php echo htmlspecialchars($request['customer_phone']); ?>" class="text-decoration-none">
                                <i class="fas fa-phone me-1"></i>
                                <?php echo htmlspecialchars($request['customer_phone']); ?>
                            </a>
                        </p>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label fw-bold">البريد الإلكتروني:</label>
                        <p class="mb-0">
                            <a href="mailto:<?php echo htmlspecialchars($request['customer_email']); ?>" class="text-decoration-none">
                                <i class="fas fa-envelope me-1"></i>
                                <?php echo htmlspecialchars($request['customer_email']); ?>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- نموذج تقديم العرض -->
    <div class="col-lg-4">
        <?php if ($existing_offer): ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        تم تقديم عرضك
                    </h5>
                </div>
                <div class="card-body text-center">
                    <i class="fas fa-handshake fs-1 text-success mb-3"></i>
                    <p>لقد قمت بتقديم عرض على هذا الطلب مسبقاً</p>
                    <a href="my-offers.php" class="btn btn-primary">
                        <i class="fas fa-eye me-1"></i>
                        عرض عروضي
                    </a>
                </div>
            </div>
        <?php elseif ($request['status'] === 'closed'): ?>
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-lock me-2"></i>
                        طلب مغلق
                    </h5>
                </div>
                <div class="card-body text-center">
                    <i class="fas fa-times-circle fs-1 text-secondary mb-3"></i>
                    <p>هذا الطلب مغلق ولا يمكن تقديم عروض عليه</p>
                </div>
            </div>
        <?php else: ?>
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-handshake me-2"></i>
                        تقديم عرض
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label class="form-label">السعر (ريال) *</label>
                            <input type="number" name="price" class="form-control" step="0.01" min="0" value="<?php echo $_POST['price'] ?? ''; ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">حالة القطعة *</label>
                            <select name="condition_type" class="form-select" required>
                                <option value="">اختر الحالة</option>
                                <?php if ($request['condition_type'] === 'both' || $request['condition_type'] === 'new'): ?>
                                    <option value="new" <?php echo ($_POST['condition_type'] ?? '') === 'new' ? 'selected' : ''; ?>>جديدة</option>
                                <?php endif; ?>
                                <?php if ($request['condition_type'] === 'both' || $request['condition_type'] === 'used'): ?>
                                    <option value="used" <?php echo ($_POST['condition_type'] ?? '') === 'used' ? 'selected' : ''; ?>>مستعملة</option>
                                <?php endif; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">فترة الضمان (بالأشهر)</label>
                            <input type="number" name="warranty_period" class="form-control" min="0" max="60" value="<?php echo $_POST['warranty_period'] ?? '0'; ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">وقت التوصيل</label>
                            <input type="text" name="delivery_time" class="form-control" value="<?php echo $_POST['delivery_time'] ?? ''; ?>" placeholder="مثال: خلال 24 ساعة">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">تكلفة التوصيل (ريال)</label>
                            <input type="number" name="delivery_cost" class="form-control" step="0.01" min="0" value="<?php echo $_POST['delivery_cost'] ?? '0'; ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">ملاحظات إضافية</label>
                            <textarea name="notes" class="form-control" rows="3" placeholder="أي معلومات إضافية عن القطعة أو الخدمة"><?php echo $_POST['notes'] ?? ''; ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">صور القطعة (اختياري)</label>
                            <input type="file" name="images[]" class="form-control" multiple accept="image/*">
                            <div class="form-text">يمكنك رفع صور للقطعة المتوفرة</div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-paper-plane me-2"></i>
                            تقديم العرض
                        </button>
                    </form>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- العروض الأخرى -->
        <?php if (!empty($other_offers)): ?>
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        العروض الأخرى (<?php echo count($other_offers); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <?php foreach ($other_offers as $offer): ?>
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between">
                                <small class="fw-bold"><?php echo htmlspecialchars($offer['business_name'] ?: $offer['vendor_name']); ?></small>
                                <small class="text-success fw-bold"><?php echo formatPrice($offer['price']); ?></small>
                            </div>
                            <small class="text-muted">
                                <?php echo $offer['condition_type'] === 'new' ? 'جديد' : 'مستعمل'; ?>
                                <?php if ($offer['warranty_period'] > 0): ?>
                                    | ضمان <?php echo $offer['warranty_period']; ?> شهر
                                <?php endif; ?>
                            </small>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal لعرض الصور -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">صورة القطعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<script>
function showImage(src) {
    document.getElementById('modalImage').src = src;
}
</script>

<?php include '../includes/footer.php'; ?>
